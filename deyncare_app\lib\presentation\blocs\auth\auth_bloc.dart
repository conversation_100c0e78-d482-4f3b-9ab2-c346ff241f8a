import 'dart:convert';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_event.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_state.dart';
import 'package:deyncare_app/domain/models/auth_token.dart';
import 'package:deyncare_app/domain/repositories/auth_repository.dart';
import 'package:deyncare_app/data/network/token/token_manager.dart';
import 'package:deyncare_app/data/network/clients/dio_client.dart';
import 'package:deyncare_app/core/utils/api_toast_handler.dart';
import 'package:deyncare_app/core/exceptions/api_exception.dart';
import 'package:logger/logger.dart';

// Notification integration
import 'package:deyncare_app/data/services/notification_service.dart';
import 'package:deyncare_app/injection_container.dart' as di;

// Import use cases
import 'package:deyncare_app/domain/usecases/auth/check_auth_status_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/login_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/register_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/logout_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/forgot_password_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/verify_email_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/change_password_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/refresh_token_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/reset_password_success_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/process_payment_use_case.dart';
import 'package:deyncare_app/domain/usecases/auth/get_available_payment_methods_use_case.dart';

/// BLoC that manages the authentication state throughout the app
/// AuthBloc manages authentication state across the app using BLoC pattern
class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final Logger _logger = Logger();

  // Use cases
  final CheckAuthStatusUseCase checkAuthStatus;
  final LoginUseCase login;
  final RegisterUseCase register;
  final LogoutUseCase logout;
  final ForgotPasswordUseCase forgotPassword;
  final VerifyEmailUseCase verifyEmail;
  final ChangePasswordUseCase changePassword;
  final RefreshTokenUseCase refreshToken;
  final ResetPasswordSuccessUseCase resetPasswordSuccess;
  final ProcessPaymentUseCase processPayment;
  final GetAvailablePaymentMethodsUseCase getAvailablePaymentMethods;

  /// Reference to the AuthRepository for retrieving current user information
  final AuthRepository authRepository;

  /// Create a new AuthBloc with use case dependencies
  AuthBloc({
    required this.checkAuthStatus,
    required this.login,
    required this.register,
    required this.logout,
    required this.forgotPassword,
    required this.verifyEmail,
    required this.changePassword,
    required this.refreshToken,
    required this.resetPasswordSuccess,
    required this.processPayment,
    required this.getAvailablePaymentMethods,
    required this.authRepository,
  }) : super(AuthInitial()) {
    _logger.d('AuthBloc: Initialized with state: ${state.runtimeType}');
    on<AppStarted>(_onAppStarted);
    on<LoggedIn>(_onLoggedIn);
    on<LoggedOut>(_onLoggedOut);
    on<TokenRefreshed>(_onTokenRefreshed);
    on<SessionExpired>(_onSessionExpired);
    
    // Add handlers for new events
    on<ForgotPasswordRequested>(_onForgotPasswordRequested);
    on<VerifyEmailRequested>(_onVerifyEmailRequested);
    on<ChangePasswordRequested>(_onChangePasswordRequested);
    on<ResetPasswordSuccess>(_onResetPasswordSuccess);
    on<ProcessPaymentRequested>(_onProcessPaymentRequested);
    on<ResendVerificationCodeRequested>(_onResendVerificationCodeRequested);
    on<InitRegistrationRequested>(_onInitRegistrationRequested, transformer: droppable());
    on<AvailablePaymentMethodsRequested>(_onAvailablePaymentMethodsRequested);
    on<CancelPaymentRequested>(_onCancelPaymentRequested);
    
    // Suspension and role validation handlers
    on<CheckAccountSuspensionRequested>(_onCheckAccountSuspensionRequested);
    on<SuspensionDetected>(_onSuspensionDetected);
    on<ValidateUserRoleRequested>(_onValidateUserRoleRequested);
    on<PeriodicUserStatusCheck>(_onPeriodicUserStatusCheck);
    on<UpdateRedirectCountdown>(_onUpdateRedirectCountdown);
    on<CompleteSuspensionRedirect>(_onCompleteSuspensionRedirect);
  }

  /// Handle app startup event - check if user is already logged in
  Future<void> _onAppStarted(
    AppStarted event,
    Emitter<AuthState> emit,
  ) async {
    _logger.d('AuthBloc: Event received - AppStarted');
    emit(AuthLoading());
    _logger.d('AuthBloc: Emitting AuthLoading for AppStarted');
    
    try {
      // Check if onboarding has been completed
      final prefs = await SharedPreferences.getInstance();
      final onboardingCompleted = prefs.getBool('onboarding_completed') ?? false;
      
      if (!onboardingCompleted) {
        _logger.d('AuthBloc: Onboarding not completed, showing onboarding');
        emit(AuthOnboardingRequired());
        return;
      }
      
      final isLoggedIn = await checkAuthStatus.execute();
      
      if (isLoggedIn) {
        try {
          // CRITICAL: Ensure DioClient has tokens loaded before declaring user authenticated
          final tokenManager = TokenManager();
          final accessToken = await tokenManager.getAccessToken();
          
          if (accessToken != null && accessToken.isNotEmpty) {
            // Get DioClient from injection container and ensure tokens are synchronized
            final dioClient = di.sl<DioClient>();
            await dioClient.syncTokensFromStorage();
            _logger.d('AuthBloc: DioClient synchronized with stored tokens');
            
            // Verify token is not expired
            final isExpired = await tokenManager.isTokenExpired();
            if (isExpired) {
              _logger.w('AuthBloc: Stored token is expired, treating as unauthenticated');
              emit(AuthUnauthenticated());
              return;
            }
          }
          
          // Get the current user from the repository
          final currentUser = await authRepository.getCurrentUser();
          
          // Create an AuthToken object to match what's expected by the AuthAuthenticated state
          final currentToken = accessToken != null 
              ? AuthToken(
                  accessToken: accessToken, 
                  refreshToken: await tokenManager.getRefreshToken() ?? '', 
                  // Parse actual expiry from token or use fallback
                  expiresAt: _parseTokenExpiry(accessToken) ?? DateTime.now().add(const Duration(hours: 1)),
                ) 
              : null;
          
          // Check if currentUser is null before emitting the authenticated state
          if (currentUser != null && currentToken != null) {
            emit(AuthAuthenticated(user: currentUser, token: currentToken));
            _logger.d('AuthBloc: Emitting AuthAuthenticated with synchronized tokens');
          } else {
            // If we can't get the user or token, consider as unauthenticated
            emit(AuthUnauthenticated());
            _logger.d('AuthBloc: Emitting AuthUnauthenticated (currentUser or token null)');
          }
        } catch (error) {
          // Log the error and emit unauthenticated state
          _logger.e('AuthBloc: Error retrieving current user or synchronizing tokens: $error');
          emit(AuthUnauthenticated());
          _logger.d('AuthBloc: Emitting AuthUnauthenticated after error');
        }
      } else {
        emit(AuthUnauthenticated());
        _logger.d('AuthBloc: Emitting AuthUnauthenticated (not logged in)');
      }
    } catch (e) {
      _logger.e('AuthBloc: Error during AppStarted: $e');
      emit(AuthFailure(message: e.toString()));
      _logger.d('AuthBloc: Emitting AuthFailure for AppStarted');
    }
  }
  
  /// Parse JWT token expiry from payload - helper method for AuthBloc
  DateTime? _parseTokenExpiry(String token) {
    try {
      final parts = token.split('.');
      if (parts.length != 3) return null;
      
      // Decode JWT payload (Base64URL)
      String payload = parts[1];
      
      // Add padding if needed for Base64 decoding
      switch (payload.length % 4) {
        case 2:
          payload += '==';
          break;
        case 3:
          payload += '=';
          break;
      }
      
      final bytes = base64Url.decode(payload);
      final json = utf8.decode(bytes);
      final Map<String, dynamic> claims = jsonDecode(json);
      
      if (claims.containsKey('exp')) {
        final exp = claims['exp'] as int;
        return DateTime.fromMillisecondsSinceEpoch(exp * 1000);
      }
    } catch (e) {
      _logger.e('AuthBloc: Error parsing token expiry: $e');
    }
    return null;
  }

  /// Handle successful login event
  Future<void> _onLoggedIn(
    LoggedIn event,
    Emitter<AuthState> emit,
  ) async {
    _logger.d('AuthBloc: Event received - LoggedIn');
    
    final user = event.user;
    
    // First check if user role is allowed for mobile app access
    if (!user.canAccessMobileApp) {
      _logger.w('AuthBloc: User role ${user.role} not allowed for mobile app access');
      
      String message;
      if (user.role == 'superAdmin') {
        message = 'Super Admin accounts use the web dashboard. Mobile app access is restricted to Admin and Employee roles only.';
      } else {
        message = 'Your account role (${user.role}) does not have access to the mobile application. Please contact your administrator.';
      }
      
      emit(AuthRoleRestricted(
        userRole: user.role,
        message: message,
        redirectCountdown: 5,
      ));
      
      _logger.d('AuthBloc: Emitting AuthRoleRestricted for login');
      return;
    }
    
    // Check if user account is suspended
    if (user.isAccountSuspended) {
      _logger.w('AuthBloc: User account is suspended: ${user.userId}');
      
      final reason = user.suspensionReason ?? 'Account suspended by administrator';
      
      emit(AuthAccountSuspended(
        reason: reason,
        suspendedAt: user.suspendedAt,
        suspendedBy: user.suspendedBy,
        redirectCountdown: 10,
      ));
      
      _logger.d('AuthBloc: Emitting AuthAccountSuspended for login');
      return;
    }
    
    // If all checks pass, proceed with normal authenticated state
    emit(AuthAuthenticated(user: event.user, token: event.token));
    _logger.d('AuthBloc: Emitting AuthAuthenticated for LoggedIn');
    
    // 🔔 Register for notifications after successful login
    try {
      // Only register for notifications if user is admin or employee
      // Note: SuperAdmin users use web dashboard, not mobile app
      if (user.isAdmin || user.isEmployee) {
        _logger.d('AuthBloc: Registering FCM token for user role: ${user.role}');
        final notificationService = di.sl<NotificationService>();
        await notificationService.registerFCMToken();
        _logger.d('AuthBloc: FCM token registration successful');
      } else {
        _logger.d('AuthBloc: Skipping FCM registration for user role: ${user.role} (mobile app not used by this role)');
      }
    } catch (e) {
      // Don't fail login if notification registration fails
      _logger.w('AuthBloc: Failed to register FCM token: $e');
    }
  }

  /// Handle logout event
  Future<void> _onLoggedOut(
    LoggedOut event,
    Emitter<AuthState> emit,
  ) async {
    _logger.d('AuthBloc: Event received - LoggedOut');
    try {
      await logout.execute();
      // 🔔 Note: FCM token cleanup would go here if backend supported unregister
      // Current backend only has /api/fcm/register and /api/fcm/test endpoints
      _logger.d('AuthBloc: FCM token cleanup skipped (unregister API not available)');
      
      // Show success toast on logout
      ApiToastHandler.handleSuccess(message: 'Successfully logged out');
      emit(AuthUnauthenticated());
      _logger.d('AuthBloc: Emitting AuthUnauthenticated for LoggedOut');
    } catch (e) {
      // Even if logout fails, we unauthenticate locally
      _logger.e('AuthBloc: Error during logout: $e');
      emit(AuthUnauthenticated());
      // Still show a toast to let the user know
      ApiToastHandler.handleInfo('You have been logged out');
      _logger.d('AuthBloc: Emitting AuthUnauthenticated after logout error');
    }
  }

  /// Handle token refresh event
  void _onTokenRefreshed(
    TokenRefreshed event,
    Emitter<AuthState> emit,
  ) {
    _logger.d('AuthBloc: Event received - TokenRefreshed');
    // If we're already authenticated, just update the token
    if (state is AuthAuthenticated) {
      final authState = state as AuthAuthenticated;
      emit(AuthAuthenticated(user: authState.user, token: event.token));
      _logger.d('AuthBloc: Emitting AuthAuthenticated for TokenRefreshed');
    }
  }

  /// Handle session expiry event
  Future<void> _onSessionExpired(
    SessionExpired event,
    Emitter<AuthState> emit,
  ) async {
    _logger.d('AuthBloc: Event received - SessionExpired');
    try {
      // Ensure we logout properly when session expires
      await logout.execute();
      // Show warning toast for session expiry
      ApiToastHandler.handleWarning('Your session has expired. Please log in again.');
      emit(AuthUnauthenticated());
      _logger.d('AuthBloc: Emitting AuthUnauthenticated for SessionExpired');
    } catch (e) {
      // Even if logout fails, we still want to unauthenticate locally
      _logger.e('AuthBloc: Error during session expiry logout: $e');
      emit(AuthUnauthenticated());
      // Still show a warning toast
      ApiToastHandler.handleWarning('Your session has expired. Please log in again.');
      _logger.d('AuthBloc: Emitting AuthUnauthenticated after session expiry error');
    }
  }

  /// Handle forgot password event
  Future<void> _onForgotPasswordRequested(
    ForgotPasswordRequested event,
    Emitter<AuthState> emit,
  ) async {
    _logger.d('AuthBloc: Event received - ForgotPasswordRequested');
    emit(AuthLoading());
    _logger.d('AuthBloc: Emitting AuthLoading for ForgotPasswordRequested');
    
    try {
      // Call the forgot password use case with the provided email
      await forgotPassword.execute(event.email);
      
      // Show success toast
      ApiToastHandler.handleSuccess(message: 'Password reset email sent to ${event.email}');
      
      // Emit success state - the user will need to check their email
      emit(AuthForgotPasswordSent());
      _logger.d('AuthBloc: Emitting AuthForgotPasswordSent');
    } catch (error) {
      // Handle any errors during the forgot password process
      _logger.e('AuthBloc: Error in forgot password: $error');
      // Show error toast
      ApiToastHandler.handleError(error, fallbackMessage: 'Failed to send password reset email');
      
      emit(AuthFailure(message: 'Failed to send password reset email. Please try again later.'));
      _logger.d('AuthBloc: Emitting AuthFailure for ForgotPasswordRequested');
    }
  }

  /// Handle verify email event
  Future<void> _onVerifyEmailRequested(
    VerifyEmailRequested event,
    Emitter<AuthState> emit,
  ) async {
    _logger.d('AuthBloc: Event received - VerifyEmailRequested. Email: ${event.email}');
    try {
      // Get the selected plan ID before emitting loading state
      String? selectedPlanId = event.selectedPlanId;
      if (selectedPlanId == null && state is AuthEmailVerificationPending) {
        selectedPlanId = (state as AuthEmailVerificationPending).selectedPlanId;
      }
      
      emit(AuthLoading(selectedPlanId: selectedPlanId));
      _logger.d('AuthBloc: Emitting AuthLoading for VerifyEmailRequested');

      final (user, registrationProgress) = await verifyEmail.execute(event.email, event.verificationCode);
      
      _logger.d('AuthBloc: Email verification successful. User status: ${user.status}, RegistrationProgress: ${registrationProgress.currentStep}, NextStep: ${registrationProgress.nextStep}');

      // Get the auth token from the user if available
      final authToken = user.toAuthToken();
      if (authToken != null) {
        _logger.d('AuthBloc: Auth token extracted from user after verification');
        // Add a TokenRefreshed event to update the token in the state
        add(TokenRefreshed(token: authToken));
      } else {
        _logger.w('AuthBloc: No auth token available after email verification');
      }

      // Check if payment is required - handle both 'payment_required' and 'needs_payment' from backend
      final bool paymentRequired = user.status == 'email_verified_pending_payment' || 
                                 registrationProgress.nextStep == 'payment_required' ||
                                 registrationProgress.nextStep == 'needs_payment';
      
      if (paymentRequired) {
        _logger.d('AuthBloc: Payment required after email verification. NextStep: ${registrationProgress.nextStep}');
        
        // Get the selected plan ID from the current state or event
        String? selectedPlanId;
        
        // First try to get from event
        if (event.selectedPlanId != null && event.selectedPlanId!.isNotEmpty) {
          selectedPlanId = event.selectedPlanId;
          _logger.d('AuthBloc: Using selectedPlanId from event: $selectedPlanId');
        } 
        // Then try from current state
        else if (state is AuthEmailVerificationPending && (state as AuthEmailVerificationPending).selectedPlanId.isNotEmpty) {
          selectedPlanId = (state as AuthEmailVerificationPending).selectedPlanId;
          _logger.d('AuthBloc: Using selectedPlanId from current state: $selectedPlanId');
        } 
        // Then from registration progress
        else if (registrationProgress.selectedPlanId != null && registrationProgress.selectedPlanId!.isNotEmpty) {
          selectedPlanId = registrationProgress.selectedPlanId;
          _logger.d('AuthBloc: Using selectedPlanId from registrationProgress: $selectedPlanId');
        } 
        // If still not found, throw error
        else {
          _logger.e('AuthBloc: Missing plan selection data. Throwing ApiException.');
          throw ApiException(
            message: 'Missing plan selection data',
            code: 'missing_plan_data',
          );
        }
        
        // Emit payment required state with the selected plan and user data
        emit(AuthPaymentRequired(
          user: user,
          selectedPlanId: selectedPlanId!,
          selectedPaymentMethod: registrationProgress.selectedPaymentMethod,
          availablePaymentMethods: null, // Will be fetched when payment screen loads
          isLoadingPaymentMethods: false,
        ));
        _logger.d('AuthBloc: Emitting AuthPaymentRequired after successful verification.');
      } else if (user.status == 'active') {
        emit(RegistrationComplete(user: user));
        _logger.d('AuthBloc: Emitting RegistrationComplete (user status active)');
      } else {
        emit(AuthEmailVerificationPending(
          user: user,
          expiresAt: DateTime.now().add(const Duration(minutes: 10)),
          selectedPlanId: registrationProgress.selectedPlanId ?? '',
          errorMessage: 'Email verified. Please complete your registration.',
        ));
        _logger.d('AuthBloc: Emitting AuthEmailVerificationPending after successful verification.');
      }
    } catch (e) {
      _logger.e('AuthBloc: Error during email verification: $e');
      await _handleError(e, emit);
    }
  }

  /// Handle resend verification code event
  Future<void> _onResendVerificationCodeRequested(
    ResendVerificationCodeRequested event,
    Emitter<AuthState> emit,
  ) async {
    _logger.d('AuthBloc: Event received - ResendVerificationCodeRequested. Email: ${event.email}');
    final currentState = state;
    if (currentState is! AuthEmailVerificationPending) {
      _logger.w('AuthBloc: ResendVerificationCodeRequested from unexpected state: ${currentState.runtimeType}');
      return;
    }

    try {
      await verifyEmail.resendVerification(currentState.user.email);
      ApiToastHandler.handleSuccess(message: 'Verification code sent successfully');
      emit(currentState.copyWith(
        expiresAt: DateTime.now().add(const Duration(minutes: 10)),
        errorMessage: null, // Clear previous error on success
      ));
      _logger.d('AuthBloc: Emitting AuthEmailVerificationPending (code resend success)');
    } catch (error) {
      _logger.e('AuthBloc: Error resending verification code: $error');
      ApiToastHandler.handleError(error, fallbackMessage: 'Failed to resend code');
      emit(currentState.copyWith(errorMessage: 'Failed to resend code. Please try again.'));
      _logger.d('AuthBloc: Emitting AuthEmailVerificationPending (code resend error)');
    }
  }

  /// Handle change password event
  Future<void> _onChangePasswordRequested(
    ChangePasswordRequested event,
    Emitter<AuthState> emit,
  ) async {
    _logger.d('AuthBloc: Event received - ChangePasswordRequested');
    emit(AuthLoading());
    _logger.d('AuthBloc: Emitting AuthLoading for ChangePasswordRequested');
    try {
      await changePassword.execute(
        currentPassword: event.currentPassword,
        newPassword: event.newPassword,
        confirmPassword: event.confirmPassword,
      );
      ApiToastHandler.handleSuccess(message: 'Password changed successfully');
      emit(AuthPasswordChanged());
      _logger.d('AuthBloc: Emitting AuthPasswordChanged');
    } catch (error) {
      _logger.e('AuthBloc: Error changing password: $error');
      ApiToastHandler.handleError(error, fallbackMessage: 'Failed to change password');
      emit(AuthFailure(message: 'Failed to change password. Please try again.'));
      _logger.d('AuthBloc: Emitting AuthFailure for ChangePasswordRequested');
    }
  }

  /// Handle reset password success event (from deep link)
  void _onResetPasswordSuccess(
    ResetPasswordSuccess event,
    Emitter<AuthState> emit,
  ) {
    _logger.d('AuthBloc: Event received - ResetPasswordSuccess');
    ApiToastHandler.handleSuccess(message: 'Password has been reset. You can now login.');
    emit(AuthUnauthenticated());
    _logger.d('AuthBloc: Emitting AuthUnauthenticated for ResetPasswordSuccess');
  }

  /// Handle process payment request
  Future<void> _onProcessPaymentRequested(
    ProcessPaymentRequested event,
    Emitter<AuthState> emit,
  ) async {
    _logger.d('AuthBloc: Event received - ProcessPaymentRequested. Plan ID: ${event.planId}');
    if (state is! AuthPaymentRequired) {
      _logger.e('AuthBloc: Invalid state to process payment: ${state.runtimeType}');
      ApiToastHandler.handleError(null, fallbackMessage: 'Invalid state to process payment');
      return;
    }
    final paymentState = state as AuthPaymentRequired;

    emit(PaymentProcessing(
      user: paymentState.user,
      planId: event.planId,
      paymentMethod: event.paymentMethod,
      paymentDetails: {
        'phoneNumber': event.phoneNumber, 
        'discountCode': event.discountCode,
        'amount': event.amount,
      },
    ));
    _logger.d('AuthBloc: Emitting PaymentProcessing');

    try {
      _logger.d('AuthBloc: Executing payment process with userId: ${paymentState.user.userId}, shopId: ${event.shopId}, planId: ${event.planId}');
      
      // Add timeout to payment processing to prevent infinite hanging
      final (user, registrationProgress) = await processPayment.execute(
        userId: paymentState.user.userId,
        shopId: event.shopId,
        planId: event.planId,
        paymentMethod: event.paymentMethod,
        phoneNumber: event.phoneNumber,
        amount: event.amount,
        discountCode: event.discountCode,
        // Offline payment fields
        payerName: event.payerName,
        payerPhone: event.payerPhone,
        notes: event.notes,
        paymentProof: event.paymentProof,
      ).timeout(
        const Duration(seconds: 90), // 90 second timeout for payment processing
        onTimeout: () {
          _logger.e('AuthBloc: Payment processing timed out after 90 seconds');
          throw ApiException(
            message: 'Payment processing timed out. Please check your connection and try again.',
            code: 'payment_timeout',
            statusCode: 408,
          );
        },
      );

      _logger.d('AuthBloc: Payment processing successful. Next step: ${registrationProgress.nextStep}');

      if (registrationProgress.nextStep == 'registration_complete') {
        emit(RegistrationComplete(user: user));
        _logger.d('AuthBloc: Emitting RegistrationComplete after payment processing');
      } else if (registrationProgress.nextStep == 'registration_complete_offline_payment_pending') {
        // Handle offline payment success - show offline payment success screen
        emit(OfflinePaymentSubmitted(
          user: user,
          planName: _getPlanDisplayName(event.planId),
          planAmount: event.amount ?? 0.0,
        ));
        _logger.d('AuthBloc: Emitting OfflinePaymentSubmitted after offline payment processing');
      } else {
        // Fallback to payment required with an error if next step is unexpected
        _logger.e('AuthBloc: Unexpected nextStep after payment processing: ${registrationProgress.nextStep}');
        ApiToastHandler.handleError(null, fallbackMessage: 'An unexpected error occurred during payment.');
        emit(paymentState.copyWith(errorMessage: 'An unexpected error occurred. Please try again.'));
        _logger.d('AuthBloc: Emitting AuthPaymentRequired with error (unexpected nextStep)');
      }
    } catch (error) {
      _logger.e('AuthBloc: Error during payment processing: $error');
      
      String errorMessage = 'Payment failed. Please try again.';
      String toastMessage = 'Payment processing failed';
      
      // Handle specific error types
      if (error is ApiException) {
        _logger.e('AuthBloc: API Exception details - code: ${error.code}, statusCode: ${error.statusCode}, message: ${error.message}');
        
        if (error.code == 'shop_not_activated' || 
            error.message.toLowerCase().contains('shop not found') || 
            error.message.toLowerCase().contains('active shop not found')) {
          errorMessage = 'Your shop is not yet activated. This is a known issue with the registration process. Please contact support for assistance.';
          toastMessage = 'Shop activation required. Please contact support.';
          _logger.e('AuthBloc: Shop activation error detected during payment');
        } else if (error.statusCode == 402) {
          // Payment gateway declined the payment
          if (error.code == 'evc_payment_declined' || error.message.toLowerCase().contains('unable to process your payment')) {
            errorMessage = 'Payment was declined by the payment provider. This could be due to:\n\n• Insufficient funds\n• Technical issues with the payment service\n• Network connectivity issues\n\nPlease try:\n• Using a different payment method\n• Checking your account balance\n• Trying again in a few minutes\n• Or choose offline payment option';
            toastMessage = 'Payment declined by EVC Plus. Try offline payment or contact support.';
          } else {
            errorMessage = 'Payment was declined. Please try a different payment method or contact support.';
            toastMessage = 'Payment declined. Try offline payment option.';
          }
        } else if (error.statusCode == 409 && error.code == 'payment_in_progress') {
          // Payment already in progress on backend
          errorMessage = 'Your payment is being processed in the background. Please wait 1-2 minutes and check your account status. Do not retry immediately.';
          toastMessage = 'Payment in progress. Please wait and check back later.';
          _logger.w('AuthBloc: Payment in progress detected - backend is processing previous request');
        } else if (error.statusCode == 400) {
          errorMessage = 'Invalid payment details. Please check your information and try again.';
          toastMessage = 'Invalid payment details';
        } else if (error.statusCode == 500) {
          errorMessage = 'Server error occurred during payment processing. Please try again or contact support.';
          toastMessage = 'Server error. Please try again.';
        } else {
          // For other API errors, use the message from the API
          errorMessage = error.message;
          toastMessage = 'Payment processing failed';
        }
        
        ApiToastHandler.handleError(error, fallbackMessage: toastMessage);
      } else {
        // For non-API errors, use the generic message
        ApiToastHandler.handleError(error, fallbackMessage: 'Payment processing failed');
      }
      
      // Revert to AuthPaymentRequired on failure
      emit(paymentState.copyWith(errorMessage: errorMessage));
      _logger.d('AuthBloc: Emitting AuthPaymentRequired with error message: $errorMessage');
    }
  }

  /// Handle initial registration requested event
  Future<void> _onInitRegistrationRequested(
    InitRegistrationRequested event,
    Emitter<AuthState> emit,
  ) async {
    _logger.d('AuthBloc: Event received - InitRegistrationRequested. Email: ${event.email}');
    emit(AuthLoading());
    _logger.d('AuthBloc: Emitting AuthLoading for InitRegistrationRequested');

    try {
      final (user, registrationProgress) = await register.execute(
        fullName: event.fullName,
        email: event.email,
        phone: event.phone,
        password: event.password,
        shopName: event.shopName,
        shopAddress: event.shopAddress,
        planType: event.planType,
        paymentMethod: event.paymentMethod,
        initialPaid: event.initialPaid,
        discountCode: event.discountCode,
      );

      _logger.d('AuthBloc: Initial registration successful. Next step: ${registrationProgress.nextStep}');

      if (registrationProgress.nextStep == 'verify_email_required') {
        emit(AuthEmailVerificationPending(
          user: user,
          expiresAt: registrationProgress.verificationCodeExpiresAt!,
          selectedPlanId: event.planType,
        ));
        _logger.d('AuthBloc: Emitting AuthEmailVerificationPending');
      } else if (registrationProgress.nextStep == 'payment_required') {
        emit(AuthPaymentRequired(
          user: user,
          selectedPlanId: registrationProgress.selectedPlanId!,
        ));
        _logger.d('AuthBloc: Emitting AuthPaymentRequired from initial registration');
      } else if (registrationProgress.nextStep == 'registration_complete') {
        emit(RegistrationComplete(user: user));
        _logger.d('AuthBloc: Emitting RegistrationComplete from initial registration');
      } else {
        emit(RegistrationInProgress(registrationProgress: registrationProgress));
        _logger.d('AuthBloc: Emitting RegistrationInProgress');
      }
    } catch (error) {
      _logger.e('AuthBloc: Error during initial registration: $error');
      ApiToastHandler.handleError(error, fallbackMessage: 'Registration failed');
      emit(AuthFailure(message: 'Registration failed. Please try again.'));
      _logger.d('AuthBloc: Emitting AuthFailure for InitRegistrationRequested');
    }
  }

  /// Handle fetching available payment methods
  Future<void> _onAvailablePaymentMethodsRequested(
    AvailablePaymentMethodsRequested event,
    Emitter<AuthState> emit,
  ) async {
    _logger.d('AuthBloc: Event received - AvailablePaymentMethodsRequested');
    if (state is! AuthPaymentRequired) {
      _logger.w('AuthBloc: AvailablePaymentMethodsRequested dispatched from invalid state: ${state.runtimeType}');
      return;
    }

    final currentState = state as AuthPaymentRequired;
    emit(currentState.copyWith(isLoadingPaymentMethods: true, errorMessage: null));
    _logger.d('AuthBloc: Emitting AuthPaymentRequired (isLoadingPaymentMethods: true)');

    try {
      final methods = await getAvailablePaymentMethods.execute(event.context);
      emit(currentState.copyWith(
        availablePaymentMethods: methods,
        isLoadingPaymentMethods: false,
      ));
      _logger.d('AuthBloc: Emitting AuthPaymentRequired (payment methods loaded)');
    } catch (error) {
      _logger.e('AuthBloc: Error fetching available payment methods: $error');
      ApiToastHandler.handleError(error, fallbackMessage: 'Could not load payment methods');
      emit(currentState.copyWith(
        isLoadingPaymentMethods: false,
        errorMessage: 'Failed to load payment methods. Please check your connection.',
      ));
      _logger.d('AuthBloc: Emitting AuthPaymentRequired with error (payment methods failed)');
    }
  }

  Future<void> _handleError(Object error, Emitter<AuthState> emit) async {
    _logger.e('AuthBloc: _handleError called with error: $error');
    
    // Don't show toast here - let the UI layer handle it to prevent duplicates
    // ApiToastHandler.handleError(error, fallbackMessage: 'An error occurred');
    
    // If we're in email verification state, we want to preserve that state
    // so the user can try again without getting stuck
    if (state is AuthEmailVerificationPending) {
      final currentState = state as AuthEmailVerificationPending;
      String errorMessage = 'Verification failed. Please try again.';
      
      if (error is ApiException) {
        errorMessage = error.message;
      } else if (error.toString().contains('type') && error.toString().contains('is not a subtype of')) {
        errorMessage = 'A technical error occurred. Please try again.';
      }
      
      emit(currentState.copyWith(errorMessage: errorMessage));
      _logger.d('AuthBloc: Preserving AuthEmailVerificationPending state with error message');
    } else if (state is AuthLoading && state.selectedPlanId != null) {
      // If we're in loading state with a selectedPlanId, try to recover to verification pending
      try {
        final user = await authRepository.getCurrentUser();
        if (user != null) {
          String errorMessage = 'Verification failed. Please try again.';
          
          if (error is ApiException) {
            errorMessage = error.message;
          } else if (error.toString().contains('type') && error.toString().contains('is not a subtype of')) {
            errorMessage = 'A technical error occurred. Please try again.';
          }
          
          emit(AuthEmailVerificationPending(
            user: user,
            expiresAt: DateTime.now().add(const Duration(minutes: 10)),
            selectedPlanId: state.selectedPlanId!,
            errorMessage: errorMessage,
          ));
          _logger.d('AuthBloc: Recovering to AuthEmailVerificationPending from AuthLoading with error message');
        } else {
          emit(AuthFailure(message: 'An error occurred. Please try again later.'));
          _logger.d('AuthBloc: Emitting AuthFailure from _handleError (no user found)');
        }
      } catch (e) {
        _logger.e('AuthBloc: Error getting current user in _handleError: $e');
        emit(AuthFailure(message: 'An error occurred. Please try again later.'));
        _logger.d('AuthBloc: Emitting AuthFailure from _handleError (error getting user)');
      }
    } else {
      // For other states, emit a general failure
      emit(AuthFailure(message: 'An error occurred. Please try again later.'));
      _logger.d('AuthBloc: Emitting AuthFailure from _handleError');
    }
  }

  /// Handle cancel payment request
  Future<void> _onCancelPaymentRequested(
    CancelPaymentRequested event,
    Emitter<AuthState> emit,
  ) async {
    _logger.d('AuthBloc: Event received - CancelPaymentRequested');
    
    if (state is! PaymentProcessing) {
      _logger.w('AuthBloc: CancelPaymentRequested dispatched from invalid state: ${state.runtimeType}');
      return;
    }
    
    final paymentState = state as PaymentProcessing;
    _logger.d('AuthBloc: Cancelling payment processing for user: ${paymentState.user.userId}');
    
    // Transition back to AuthPaymentRequired state so user can try again
    emit(AuthPaymentRequired(
      user: paymentState.user,
      selectedPlanId: paymentState.planId,
      errorMessage: 'Payment was cancelled. You can try again when ready.',
    ));
    
    _logger.d('AuthBloc: Emitting AuthPaymentRequired after payment cancellation');
    
    // Only fetch available payment methods if we're still in a valid state for payment
    if (state is AuthPaymentRequired) {
      add(const AvailablePaymentMethodsRequested(context: 'subscription'));
    }
  }

  /// Handle account suspension check
  Future<void> _onCheckAccountSuspensionRequested(
    CheckAccountSuspensionRequested event,
    Emitter<AuthState> emit,
  ) async {
    _logger.d('AuthBloc: Event received - CheckAccountSuspensionRequested for user: ${event.user.userId}');
    
    final user = event.user;
    
    // Check if user account is suspended
    if (user.isAccountSuspended) {
      _logger.w('AuthBloc: User account is suspended: ${user.userId}');
      
      final reason = user.suspensionReason ?? 'Account suspended by administrator';
      
      emit(AuthAccountSuspended(
        reason: reason,
        suspendedAt: user.suspendedAt,
        suspendedBy: user.suspendedBy,
        redirectCountdown: 10,
      ));
      
      _logger.d('AuthBloc: Emitting AuthAccountSuspended');
      return;
    }
    
    _logger.d('AuthBloc: User account is not suspended, continuing with normal flow');
  }

  /// Handle suspension detected during API calls (e.g., during login)
  Future<void> _onSuspensionDetected(
    SuspensionDetected event,
    Emitter<AuthState> emit,
  ) async {
    _logger.d('AuthBloc: Event received - SuspensionDetected');
    _logger.w('AuthBloc: Account suspension detected during API call - Reason: ${event.reason}');
    
    emit(AuthAccountSuspended(
      reason: event.reason,
      suspendedAt: event.suspendedAt,
      suspendedBy: event.suspendedBy,
      redirectCountdown: 10,
    ));
    
    _logger.d('AuthBloc: Emitting AuthAccountSuspended for detected suspension');
  }

  /// Handle user role validation for mobile app access
  Future<void> _onValidateUserRoleRequested(
    ValidateUserRoleRequested event,
    Emitter<AuthState> emit,
  ) async {
    _logger.d('AuthBloc: Event received - ValidateUserRoleRequested for user: ${event.user.userId}');
    
    final user = event.user;
    
    // Check if user role is allowed for mobile app access
    if (!user.canAccessMobileApp) {
      _logger.w('AuthBloc: User role ${user.role} not allowed for mobile app access');
      
      String message;
      if (user.role == 'superAdmin') {
        message = 'Super Admin accounts use the web dashboard. Mobile app access is restricted to Admin and Employee roles only.';
      } else {
        message = 'Your account role (${user.role}) does not have access to the mobile application. Please contact your administrator.';
      }
      
      emit(AuthRoleRestricted(
        userRole: user.role,
        message: message,
        redirectCountdown: 5,
      ));
      
      _logger.d('AuthBloc: Emitting AuthRoleRestricted');
      return;
    }
    
    _logger.d('AuthBloc: User role ${user.role} is allowed for mobile app access');
  }

  /// Handle periodic user status check (called while user is actively using the app)
  Future<void> _onPeriodicUserStatusCheck(
    PeriodicUserStatusCheck event,
    Emitter<AuthState> emit,
  ) async {
    _logger.d('AuthBloc: Event received - PeriodicUserStatusCheck');
    
    // Only perform checks if user is currently authenticated
    if (state is! AuthAuthenticated) {
      _logger.d('AuthBloc: Skipping periodic check - user not authenticated');
      return;
    }
    
    final authState = state as AuthAuthenticated;
    
    try {
      // Fetch current user info from backend to check for status changes
      final currentUser = await authRepository.getCurrentUser();
      
      if (currentUser == null) {
        _logger.w('AuthBloc: Current user is null during periodic check - logging out');
        add(LoggedOut());
        return;
      }
      
      // Check if user has been suspended since last check
      if (currentUser.isAccountSuspended && !authState.user.isAccountSuspended) {
        _logger.w('AuthBloc: User was suspended during active session: ${currentUser.userId}');
        
        final reason = currentUser.suspensionReason ?? 'Account suspended by administrator';
        
        emit(AuthAccountSuspended(
          reason: reason,
          suspendedAt: currentUser.suspendedAt,
          suspendedBy: currentUser.suspendedBy,
          redirectCountdown: 10,
        ));
        
        _logger.d('AuthBloc: Emitting AuthAccountSuspended during periodic check');
        return;
      }
      
      // Check if user role changed to one that can't access mobile app
      if (!currentUser.canAccessMobileApp && authState.user.canAccessMobileApp) {
        _logger.w('AuthBloc: User role changed to ${currentUser.role} - mobile access revoked');
        
        emit(AuthRoleRestricted(
          userRole: currentUser.role,
          message: 'Your account role has been changed. Mobile app access is no longer available.',
          redirectCountdown: 5,
        ));
        
        _logger.d('AuthBloc: Emitting AuthRoleRestricted during periodic check');
        return;
      }
      
      // If user status is still valid, update the authenticated state with fresh user data
      if (currentUser.isActive && currentUser.canAccessMobileApp && !currentUser.isAccountSuspended) {
        emit(AuthAuthenticated(user: currentUser, token: authState.token));
        _logger.d('AuthBloc: Updated AuthAuthenticated state with fresh user data');
      }
      
    } catch (error) {
      _logger.e('AuthBloc: Error during periodic user status check: $error');
      
      // If it's an authentication error, log the user out
      if (error is ApiException && (error.code == 'unauthorized' || error.statusCode == 401)) {
        _logger.w('AuthBloc: Unauthorized during periodic check - logging out');
        add(LoggedOut());
      }
      // For other errors, continue silently to avoid disrupting user experience
    }
  }

  /// Handle countdown timer update for suspension/role restriction dialogs
  void _onUpdateRedirectCountdown(
    UpdateRedirectCountdown event,
    Emitter<AuthState> emit,
  ) {
    _logger.d('AuthBloc: Event received - UpdateRedirectCountdown: ${event.countdown}');
    
    if (state is AuthAccountSuspended) {
      final suspensionState = state as AuthAccountSuspended;
      emit(suspensionState.copyWith(redirectCountdown: event.countdown));
      _logger.d('AuthBloc: Updated AuthAccountSuspended countdown to ${event.countdown}');
    } else if (state is AuthRoleRestricted) {
      final roleState = state as AuthRoleRestricted;
      emit(roleState.copyWith(redirectCountdown: event.countdown));
      _logger.d('AuthBloc: Updated AuthRoleRestricted countdown to ${event.countdown}');
    }
  }

  /// Handle completion of suspension redirect (when timer reaches 0 or user dismisses)
  Future<void> _onCompleteSuspensionRedirect(
    CompleteSuspensionRedirect event,
    Emitter<AuthState> emit,
  ) async {
    _logger.d('AuthBloc: Event received - CompleteSuspensionRedirect');
    
    if (state is AuthAccountSuspended || state is AuthRoleRestricted) {
      _logger.d('AuthBloc: Completing suspension/role restriction redirect - logging out');
      
      // Logout the user and redirect to login screen
      try {
        await logout.execute();
      } catch (error) {
        _logger.e('AuthBloc: Error during logout after suspension redirect: $error');
      }
      
      emit(AuthUnauthenticated());
      _logger.d('AuthBloc: Emitting AuthUnauthenticated after suspension redirect');
    }
  }

  /// Get display name for plan
  String _getPlanDisplayName(String planId) {
    switch (planId.toLowerCase()) {
      case 'trial':
        return 'Trial Plan';
      case 'basic':
        return 'Basic Plan';
      case 'premium':
        return 'Premium Plan';
      case 'enterprise':
        return 'Enterprise Plan';
      case 'monthly':
        return 'Monthly Plan';
      case 'yearly':
        return 'Yearly Plan';
      default:
        return 'Selected Plan';
    }
  }
}
