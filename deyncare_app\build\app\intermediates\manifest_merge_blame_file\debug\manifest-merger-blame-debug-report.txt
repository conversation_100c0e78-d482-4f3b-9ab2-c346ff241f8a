1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.deyncare.app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:5:5-67
15-->C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:5:22-64
16    <!-- Network state permissions - required for connectivity monitoring -->
17    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- Storage permissions - required for PDF file generation and external storage access -->
17-->C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:7:5-79
17-->C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:7:22-76
18    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
18-->C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:10:5-81
18-->C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:10:22-78
19    <uses-permission
19-->C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:11:5-80
20        android:name="android.permission.READ_EXTERNAL_STORAGE"
20-->C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:11:22-77
21        android:maxSdkVersion="32" /> <!-- For Android 13+ (API 33+) - Manage external storage permission -->
21-->[:file_picker] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:9:9-35
22    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
22-->C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:13:5-111
22-->C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:13:22-79
23    <!--
24 Required to query activities that can process text, see:
25         https://developer.android.com/training/package-visibility and
26         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
27
28         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
29    -->
30    <queries>
30-->C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:98:5-103:15
31        <intent>
31-->C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:99:9-102:18
32            <action android:name="android.intent.action.PROCESS_TEXT" />
32-->C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:100:13-72
32-->C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:100:21-70
33
34            <data android:mimeType="text/plain" />
34-->C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:101:13-50
34-->C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:101:19-48
35        </intent>
36        <intent>
36-->[:file_picker] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:12:9-16:18
37            <action android:name="android.intent.action.GET_CONTENT" />
37-->[:file_picker] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-72
37-->[:file_picker] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:13:21-69
38
39            <data android:mimeType="*/*" />
39-->C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:101:13-50
39-->C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:101:19-48
40        </intent>
41    </queries>
42
43    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Permissions options for the `notification` group -->
43-->[:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-68
43-->[:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:8:22-65
44    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Required by older versions of Google Play services to create IID tokens -->
44-->[:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:11:5-77
44-->[:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:11:22-74
45    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
45-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:26:5-82
45-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:26:22-79
46    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
46-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f5d009877ead73fa58d6e4160bb4830\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:25:5-79
46-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f5d009877ead73fa58d6e4160bb4830\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:25:22-76
47    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
47-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f5d009877ead73fa58d6e4160bb4830\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:26:5-88
47-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f5d009877ead73fa58d6e4160bb4830\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:26:22-85
48    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
48-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f5d009877ead73fa58d6e4160bb4830\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:27:5-82
48-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f5d009877ead73fa58d6e4160bb4830\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:27:22-79
49    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
49-->[:network_info_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\network_info_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-76
49-->[:network_info_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\network_info_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:8:22-73
50    <uses-permission android:name="android.permission.VIBRATE" />
50-->[:flutter_local_notifications] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-66
50-->[:flutter_local_notifications] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:7:22-63
51    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
51-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7d679a13a627326f3f4534e4c3dea4fb\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:26:5-110
51-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7d679a13a627326f3f4534e4c3dea4fb\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:26:22-107
52
53    <permission
53-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\9b9cc03f2bd74b7af0023cf87b6a2633\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
54        android:name="com.deyncare.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
54-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\9b9cc03f2bd74b7af0023cf87b6a2633\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
55        android:protectionLevel="signature" />
55-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\9b9cc03f2bd74b7af0023cf87b6a2633\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
56
57    <uses-permission android:name="com.deyncare.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- Permission will be merged into the manifest of the hosting app. -->
57-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\9b9cc03f2bd74b7af0023cf87b6a2633\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
57-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\9b9cc03f2bd74b7af0023cf87b6a2633\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
58    <!-- Is required to launch foreground extraction service for targetSdkVersion 28+. -->
59    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
59-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:13:5-77
59-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:13:22-74
60
61    <application
62        android:name="android.app.Application"
63        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
63-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\9b9cc03f2bd74b7af0023cf87b6a2633\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
64        android:debuggable="true"
65        android:extractNativeLibs="true"
66        android:icon="@mipmap/launcher_icon"
67        android:label="Deyncare"
68        android:networkSecurityConfig="@xml/network_security_config"
69        android:usesCleartextTraffic="true" >
70
71        <!-- Prevent Digital Turbine/Ignite initialization that crashes on some Samsung devices -->
72        <provider
73            android:name="androidx.startup.InitializationProvider"
74            android:authorities="com.deyncare.app.androidx-startup"
75            android:exported="false" >
76            <meta-data
76-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\06933728235e73d7d65b1a63335411d5\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
77                android:name="androidx.emoji2.text.EmojiCompatInitializer"
77-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\06933728235e73d7d65b1a63335411d5\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
78                android:value="androidx.startup" />
78-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\06933728235e73d7d65b1a63335411d5\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
79            <meta-data
79-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\af03bdcd696f8df9133f9358f674316b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
80                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
80-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\af03bdcd696f8df9133f9358f674316b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
81                android:value="androidx.startup" />
81-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\af03bdcd696f8df9133f9358f674316b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
82            <meta-data
82-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
83                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
83-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
84                android:value="androidx.startup" />
84-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
85        </provider>
86
87        <!-- FileProvider for secure file sharing -->
88        <provider
89            android:name="androidx.core.content.FileProvider"
90            android:authorities="com.deyncare.app.fileprovider"
91            android:exported="false"
92            android:grantUriPermissions="true" >
93            <meta-data
93-->[:share_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:18:13-20:68
94                android:name="android.support.FILE_PROVIDER_PATHS"
94-->[:share_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:19:17-67
95                android:resource="@xml/file_paths" />
95-->[:share_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:20:17-65
96        </provider>
97
98        <activity
99            android:name="com.deyncare.app.MainActivity"
100            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
101            android:exported="true"
102            android:hardwareAccelerated="true"
103            android:launchMode="singleTop"
104            android:taskAffinity=""
105            android:theme="@style/LaunchTheme"
106            android:windowSoftInputMode="adjustResize" >
107
108            <!--
109                 Specifies an Android theme to apply to this Activity as soon as
110                 the Android process has started. This theme is visible to the user
111                 while the Flutter UI initializes. After that, this theme continues
112                 to determine the Window background behind the Flutter UI.
113            -->
114            <meta-data
115                android:name="io.flutter.embedding.android.NormalTheme"
116                android:resource="@style/NormalTheme" />
117
118            <intent-filter>
119                <action android:name="android.intent.action.MAIN" />
120
121                <category android:name="android.intent.category.LAUNCHER" />
122            </intent-filter>
123
124            <!-- 🔗 Deep Link Configuration for Push Notifications -->
125            <intent-filter android:autoVerify="true" >
126                <action android:name="android.intent.action.VIEW" />
127
128                <category android:name="android.intent.category.DEFAULT" />
129                <category android:name="android.intent.category.BROWSABLE" />
130
131                <data android:scheme="deyncare" />
131-->C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:101:13-50
132            </intent-filter>
133        </activity>
134        <!--
135             Don't delete the meta-data below.
136             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
137        -->
138        <meta-data
139            android:name="flutterEmbedding"
140            android:value="2" />
141
142        <service
142-->[:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:14:9-17:72
143            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
143-->[:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:15:13-107
144            android:exported="false"
144-->[:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-37
145            android:permission="android.permission.BIND_JOB_SERVICE" />
145-->[:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:17:13-69
146        <service
146-->[:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:18:9-24:19
147            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
147-->[:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:19:13-97
148            android:exported="false" >
148-->[:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:20:13-37
149            <intent-filter>
149-->[:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:21:13-23:29
150                <action android:name="com.google.firebase.MESSAGING_EVENT" />
150-->[:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:22:17-78
150-->[:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:22:25-75
151            </intent-filter>
152        </service>
153
154        <receiver
154-->[:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:26:9-33:20
155            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
155-->[:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:27:13-98
156            android:exported="true"
156-->[:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:28:13-36
157            android:permission="com.google.android.c2dm.permission.SEND" >
157-->[:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:29:13-73
158            <intent-filter>
158-->[:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:30:13-32:29
159                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
159-->[:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:31:17-81
159-->[:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:31:25-78
160            </intent-filter>
161        </receiver>
162
163        <service
163-->[:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:35:9-39:19
164            android:name="com.google.firebase.components.ComponentDiscoveryService"
164-->[:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:35:18-89
165            android:directBootAware="true"
165-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\db603b42ab4ee8471ccf00b02b70e058\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:32:13-43
166            android:exported="false" >
166-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:56:13-37
167            <meta-data
167-->[:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:36:13-38:85
168                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
168-->[:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:37:17-128
169                android:value="com.google.firebase.components.ComponentRegistrar" />
169-->[:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:38:17-82
170            <meta-data
170-->[:firebase_analytics] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_analytics\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-15:85
171                android:name="com.google.firebase.components:io.flutter.plugins.firebase.analytics.FlutterFirebaseAppRegistrar"
171-->[:firebase_analytics] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_analytics\intermediates\merged_manifest\debug\AndroidManifest.xml:14:17-128
172                android:value="com.google.firebase.components.ComponentRegistrar" />
172-->[:firebase_analytics] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_analytics\intermediates\merged_manifest\debug\AndroidManifest.xml:15:17-82
173            <meta-data
173-->[:firebase_core] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
174                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
174-->[:firebase_core] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-124
175                android:value="com.google.firebase.components.ComponentRegistrar" />
175-->[:firebase_core] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
176            <meta-data
176-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:57:13-59:85
177                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
177-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:58:17-122
178                android:value="com.google.firebase.components.ComponentRegistrar" />
178-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:59:17-82
179            <meta-data
179-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:60:13-62:85
180                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
180-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:61:17-119
181                android:value="com.google.firebase.components.ComponentRegistrar" />
181-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:62:17-82
182            <meta-data
182-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cc572472b49570e8646bb589993a119b\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:32:13-34:85
183                android:name="com.google.firebase.components:com.google.firebase.iid.Registrar"
183-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cc572472b49570e8646bb589993a119b\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:33:17-96
184                android:value="com.google.firebase.components.ComponentRegistrar" />
184-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cc572472b49570e8646bb589993a119b\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:34:17-82
185            <meta-data
185-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f5d009877ead73fa58d6e4160bb4830\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:37:13-39:85
186                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
186-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f5d009877ead73fa58d6e4160bb4830\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:38:17-139
187                android:value="com.google.firebase.components.ComponentRegistrar" />
187-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f5d009877ead73fa58d6e4160bb4830\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:39:17-82
188            <meta-data
188-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\289a5ecd1461865315d1031ce26cad65\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
189                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
189-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\289a5ecd1461865315d1031ce26cad65\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
190                android:value="com.google.firebase.components.ComponentRegistrar" />
190-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\289a5ecd1461865315d1031ce26cad65\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
191            <meta-data
191-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\289a5ecd1461865315d1031ce26cad65\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
192                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
192-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\289a5ecd1461865315d1031ce26cad65\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
193                android:value="com.google.firebase.components.ComponentRegistrar" />
193-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\289a5ecd1461865315d1031ce26cad65\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
194            <meta-data
194-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\1027ddadf767d22a0555e7d7d1604288\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:12:13-14:85
195                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
195-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\1027ddadf767d22a0555e7d7d1604288\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:13:17-116
196                android:value="com.google.firebase.components.ComponentRegistrar" />
196-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\1027ddadf767d22a0555e7d7d1604288\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:14:17-82
197            <meta-data
197-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\db603b42ab4ee8471ccf00b02b70e058\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:35:13-37:85
198                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
198-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\db603b42ab4ee8471ccf00b02b70e058\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:36:17-109
199                android:value="com.google.firebase.components.ComponentRegistrar" />
199-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\db603b42ab4ee8471ccf00b02b70e058\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:37:17-82
200            <meta-data
200-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\b5106d3e1c3f02e0d77be4cf490616bd\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
201                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
201-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\b5106d3e1c3f02e0d77be4cf490616bd\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
202                android:value="com.google.firebase.components.ComponentRegistrar" />
202-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\b5106d3e1c3f02e0d77be4cf490616bd\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
203        </service>
204
205        <provider
205-->[:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:41:9-45:38
206            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
206-->[:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:42:13-102
207            android:authorities="com.deyncare.app.flutterfirebasemessaginginitprovider"
207-->[:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:43:13-88
208            android:exported="false"
208-->[:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:44:13-37
209            android:initOrder="99" />
209-->[:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:45:13-35
210
211        <receiver
211-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:29:9-40:20
212            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
212-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:30:13-78
213            android:exported="true"
213-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:31:13-36
214            android:permission="com.google.android.c2dm.permission.SEND" >
214-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:32:13-73
215            <intent-filter>
215-->[:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:30:13-32:29
216                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
216-->[:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:31:17-81
216-->[:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:31:25-78
217            </intent-filter>
218
219            <meta-data
219-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:37:13-39:40
220                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
220-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:38:17-92
221                android:value="true" />
221-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:39:17-37
222        </receiver>
223        <!--
224             FirebaseMessagingService performs security checks at runtime,
225             but set to not exported to explicitly avoid allowing another app to call it.
226        -->
227        <service
227-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:46:9-53:19
228            android:name="com.google.firebase.messaging.FirebaseMessagingService"
228-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:47:13-82
229            android:directBootAware="true"
229-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:48:13-43
230            android:exported="false" >
230-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:49:13-37
231            <intent-filter android:priority="-500" >
231-->[:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:21:13-23:29
232                <action android:name="com.google.firebase.MESSAGING_EVENT" />
232-->[:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:22:17-78
232-->[:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:22:25-75
233            </intent-filter>
234        </service>
235
236        <property
236-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f5d009877ead73fa58d6e4160bb4830\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:30:9-32:61
237            android:name="android.adservices.AD_SERVICES_CONFIG"
237-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f5d009877ead73fa58d6e4160bb4830\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:31:13-65
238            android:resource="@xml/ga_ad_services_config" />
238-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f5d009877ead73fa58d6e4160bb4830\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:32:13-58
239        <!--
240           Declares a provider which allows us to store files to share in
241           '.../caches/share_plus' and grant the receiving action access
242        -->
243        <provider
243-->[:share_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:13:9-21:20
244            android:name="dev.fluttercommunity.plus.share.ShareFileProvider"
244-->[:share_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-77
245            android:authorities="com.deyncare.app.flutter.share_provider"
245-->[:share_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:15:13-74
246            android:exported="false"
246-->[:share_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-37
247            android:grantUriPermissions="true" >
247-->[:share_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:17:13-47
248            <meta-data
248-->[:share_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:18:13-20:68
249                android:name="android.support.FILE_PROVIDER_PATHS"
249-->[:share_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:19:17-67
250                android:resource="@xml/flutter_share_file_paths" />
250-->[:share_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:20:17-65
251        </provider>
252        <!--
253           This manifest declared broadcast receiver allows us to use an explicit
254           Intent when creating a PendingItent to be informed of the user's choice
255        -->
256        <receiver
256-->[:share_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:26:9-32:20
257            android:name="dev.fluttercommunity.plus.share.SharePlusPendingIntent"
257-->[:share_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:27:13-82
258            android:exported="false" >
258-->[:share_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:28:13-37
259            <intent-filter>
259-->[:share_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:29:13-31:29
260                <action android:name="EXTRA_CHOSEN_COMPONENT" />
260-->[:share_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:30:17-65
260-->[:share_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:30:25-62
261            </intent-filter>
262        </receiver>
263
264        <provider
264-->[:image_picker_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:9-17:20
265            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
265-->[:image_picker_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-82
266            android:authorities="com.deyncare.app.flutter.image_provider"
266-->[:image_picker_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-74
267            android:exported="false"
267-->[:image_picker_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-37
268            android:grantUriPermissions="true" >
268-->[:image_picker_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-47
269            <meta-data
269-->[:share_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:18:13-20:68
270                android:name="android.support.FILE_PROVIDER_PATHS"
270-->[:share_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:19:17-67
271                android:resource="@xml/flutter_image_picker_file_paths" />
271-->[:share_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:20:17-65
272        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
273        <service
273-->[:image_picker_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:19:9-31:19
274            android:name="com.google.android.gms.metadata.ModuleDependencies"
274-->[:image_picker_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:20:13-78
275            android:enabled="false"
275-->[:image_picker_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:21:13-36
276            android:exported="false" >
276-->[:image_picker_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:22:13-37
277            <intent-filter>
277-->[:image_picker_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:24:13-26:29
278                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
278-->[:image_picker_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:25:17-94
278-->[:image_picker_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:25:25-91
279            </intent-filter>
280
281            <meta-data
281-->[:image_picker_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:28:13-30:36
282                android:name="photopicker_activity:0:required"
282-->[:image_picker_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:29:17-63
283                android:value="" />
283-->[:image_picker_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:30:17-33
284        </service>
285
286        <activity
286-->[:url_launcher_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-11:74
287            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
287-->[:url_launcher_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-74
288            android:exported="false"
288-->[:url_launcher_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-37
289            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
289-->[:url_launcher_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-71
290
291        <provider
291-->[:open_file_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:9-19:20
292            android:name="com.crazecoder.openfile.FileProvider"
292-->[:open_file_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-64
293            android:authorities="com.deyncare.app.fileProvider.com.crazecoder.openfile"
293-->[:open_file_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-88
294            android:exported="false"
294-->[:open_file_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-37
295            android:grantUriPermissions="true"
295-->[:open_file_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-47
296            android:requestLegacyExternalStorage="true" >
296-->[:open_file_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-56
297            <meta-data
297-->[:share_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:18:13-20:68
298                android:name="android.support.FILE_PROVIDER_PATHS"
298-->[:share_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:19:17-67
299                android:resource="@xml/filepaths" />
299-->[:share_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:20:17-65
300        </provider>
301        <provider
301-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\db603b42ab4ee8471ccf00b02b70e058\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:23:9-28:39
302            android:name="com.google.firebase.provider.FirebaseInitProvider"
302-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\db603b42ab4ee8471ccf00b02b70e058\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:24:13-77
303            android:authorities="com.deyncare.app.firebaseinitprovider"
303-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\db603b42ab4ee8471ccf00b02b70e058\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:25:13-72
304            android:directBootAware="true"
304-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\db603b42ab4ee8471ccf00b02b70e058\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:26:13-43
305            android:exported="false"
305-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\db603b42ab4ee8471ccf00b02b70e058\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:27:13-37
306            android:initOrder="100" />
306-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\db603b42ab4ee8471ccf00b02b70e058\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:28:13-36
307
308        <receiver
308-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7d679a13a627326f3f4534e4c3dea4fb\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:29:9-33:20
309            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
309-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7d679a13a627326f3f4534e4c3dea4fb\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:30:13-85
310            android:enabled="true"
310-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7d679a13a627326f3f4534e4c3dea4fb\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:31:13-35
311            android:exported="false" >
311-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7d679a13a627326f3f4534e4c3dea4fb\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:32:13-37
312        </receiver>
313
314        <service
314-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7d679a13a627326f3f4534e4c3dea4fb\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:35:9-38:40
315            android:name="com.google.android.gms.measurement.AppMeasurementService"
315-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7d679a13a627326f3f4534e4c3dea4fb\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:36:13-84
316            android:enabled="true"
316-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7d679a13a627326f3f4534e4c3dea4fb\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:37:13-35
317            android:exported="false" />
317-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7d679a13a627326f3f4534e4c3dea4fb\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:38:13-37
318        <service
318-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7d679a13a627326f3f4534e4c3dea4fb\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:39:9-43:72
319            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
319-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7d679a13a627326f3f4534e4c3dea4fb\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:40:13-87
320            android:enabled="true"
320-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7d679a13a627326f3f4534e4c3dea4fb\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:41:13-35
321            android:exported="false"
321-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7d679a13a627326f3f4534e4c3dea4fb\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:42:13-37
322            android:permission="android.permission.BIND_JOB_SERVICE" />
322-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7d679a13a627326f3f4534e4c3dea4fb\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:43:13-69
323
324        <activity
324-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\0879a96deba1802dbda911e1e856ab70\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
325            android:name="com.google.android.gms.common.api.GoogleApiActivity"
325-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\0879a96deba1802dbda911e1e856ab70\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:19-85
326            android:exported="false"
326-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\0879a96deba1802dbda911e1e856ab70\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:22:19-43
327            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
327-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\0879a96deba1802dbda911e1e856ab70\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:21:19-78
328
329        <uses-library
329-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5d63ccd608ec6e693fb6e6a6ff4129d\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
330            android:name="androidx.window.extensions"
330-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5d63ccd608ec6e693fb6e6a6ff4129d\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
331            android:required="false" />
331-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5d63ccd608ec6e693fb6e6a6ff4129d\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
332        <uses-library
332-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5d63ccd608ec6e693fb6e6a6ff4129d\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
333            android:name="androidx.window.sidecar"
333-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5d63ccd608ec6e693fb6e6a6ff4129d\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
334            android:required="false" />
334-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5d63ccd608ec6e693fb6e6a6ff4129d\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
335        <uses-library
335-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\9d21234604d5de133528520faad3c971\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
336            android:name="android.ext.adservices"
336-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\9d21234604d5de133528520faad3c971\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
337            android:required="false" />
337-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\9d21234604d5de133528520faad3c971\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
338
339        <meta-data
339-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f411ab778a71bcd166e4feba4d61db04\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
340            android:name="com.google.android.gms.version"
340-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f411ab778a71bcd166e4feba4d61db04\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
341            android:value="@integer/google_play_services_version" />
341-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f411ab778a71bcd166e4feba4d61db04\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
342
343        <receiver
343-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
344            android:name="androidx.profileinstaller.ProfileInstallReceiver"
344-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
345            android:directBootAware="false"
345-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
346            android:enabled="true"
346-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
347            android:exported="true"
347-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
348            android:permission="android.permission.DUMP" >
348-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
349            <intent-filter>
349-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
350                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
350-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
350-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
351            </intent-filter>
352            <intent-filter>
352-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
353                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
353-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
353-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
354            </intent-filter>
355            <intent-filter>
355-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
356                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
356-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
356-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
357            </intent-filter>
358            <intent-filter>
358-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
359                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
359-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
359-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
360            </intent-filter>
361        </receiver>
362
363        <service
363-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\d00ad81142e4ea506fba000731778171\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
364            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
364-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\d00ad81142e4ea506fba000731778171\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
365            android:exported="false" >
365-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\d00ad81142e4ea506fba000731778171\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
366            <meta-data
366-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\d00ad81142e4ea506fba000731778171\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
367                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
367-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\d00ad81142e4ea506fba000731778171\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
368                android:value="cct" />
368-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\d00ad81142e4ea506fba000731778171\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
369        </service>
370        <service
370-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\87c6bc5d9351f73b7b7de3739170458b\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
371            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
371-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\87c6bc5d9351f73b7b7de3739170458b\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
372            android:exported="false"
372-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\87c6bc5d9351f73b7b7de3739170458b\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
373            android:permission="android.permission.BIND_JOB_SERVICE" >
373-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\87c6bc5d9351f73b7b7de3739170458b\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
374        </service>
375
376        <receiver
376-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\87c6bc5d9351f73b7b7de3739170458b\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
377            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
377-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\87c6bc5d9351f73b7b7de3739170458b\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
378            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
378-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\87c6bc5d9351f73b7b7de3739170458b\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
379        <activity
379-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:18:9-24:45
380            android:name="com.google.android.play.core.missingsplits.PlayCoreMissingSplitsActivity"
380-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:19:13-100
381            android:enabled="false"
381-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:20:13-36
382            android:exported="false"
382-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:21:13-37
383            android:launchMode="singleInstance"
383-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:22:13-48
384            android:process=":playcore_missing_splits_activity"
384-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:23:13-64
385            android:stateNotNeeded="true" />
385-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:24:13-42
386        <activity
386-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:25:9-29:65
387            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
387-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:26:13-93
388            android:exported="false"
388-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:27:13-37
389            android:stateNotNeeded="true"
389-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:28:13-42
390            android:theme="@style/Theme.PlayCore.Transparent" /> <!-- The services will be merged into the manifest of the hosting app. -->
390-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:29:13-62
391        <service
391-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:32:9-39:19
392            android:name="com.google.android.play.core.assetpacks.AssetPackExtractionService"
392-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:33:13-94
393            android:enabled="false"
393-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:34:13-36
394            android:exported="true" >
394-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:35:13-36
395            <meta-data
395-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:36:13-38:41
396                android:name="com.google.android.play.core.assetpacks.versionCode"
396-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:37:17-83
397                android:value="11003" />
397-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:38:17-38
398        </service>
399        <service
399-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:40:9-43:40
400            android:name="com.google.android.play.core.assetpacks.ExtractionForegroundService"
400-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:41:13-95
401            android:enabled="false"
401-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:42:13-36
402            android:exported="false" />
402-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:43:13-37
403    </application>
404
405</manifest>
