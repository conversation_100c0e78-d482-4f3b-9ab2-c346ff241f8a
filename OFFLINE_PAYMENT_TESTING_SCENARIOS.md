# 🧪 Comprehensive Offline Payment Registration Testing Scenarios

## 📋 Overview
This document provides complete testing scenarios for the DeynCare offline payment registration system, including exact API endpoints, payloads, and expected responses based on the codebase analysis.

---

## 🔧 Prerequisites

### Authentication Setup
```bash
# 1. Register a new user first
curl -X POST http://localhost:5000/api/register/init \
  -H "Content-Type: application/json" \
  -d '{
    "fullName": "<PERSON>",
    "email": "<EMAIL>",
    "phone": "+************",
    "password": "SecurePass123!",
    "shopName": "Ahmed Electronics",
    "shopAddress": "Bakara Market, Mogadishu",
    "planType": "monthly"
  }'

# 2. Verify email (get verification code from email/logs)
curl -X POST http://localhost:5000/api/register/verify-email \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "verificationCode": "123456"
  }'

# 3. Login to get access token
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePass123!"
  }'

# Extract the access_token from response for use in subsequent requests
export ACCESS_TOKEN="your_jwt_token_here"
```

---

## 🎯 Test Scenarios

### **Scenario 1: Basic Offline Payment (No File Upload)**

#### Endpoint: `POST /api/register/pay`
```bash
curl -X POST http://localhost:5000/api/register/pay \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "planType": "monthly",
    "paymentMethod": "offline"
  }'
```

#### Expected Response:
```json
{
  "success": true,
  "message": "Payment skipped. Complete payment later.",
  "data": {
    "user": {
      "userId": "USR_123456",
      "email": "<EMAIL>",
      "status": "pending_payment",
      "isPaid": false
    },
    "shop": {
      "shopId": "SHP_789012",
      "shopName": "Ahmed Electronics",
      "status": "pending"
    },
    "subscription": {
      "subscriptionId": "SUB_345678",
      "status": "pending_payment"
    },
    "nextStep": "registration_complete_offline_payment_pending"
  }
}
```

---

### **Scenario 2: Enhanced Offline Payment with Cash Method**

#### Endpoint: `POST /api/register/pay`
```bash
curl -X POST http://localhost:5000/api/register/pay \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "planType": "monthly",
    "paymentMethod": "Cash",
    "payerName": "Ahmed Hassan",
    "payerPhone": "+************",
    "notes": "Paid cash at DeynCare office"
  }'
```

#### Expected Response:
```json
{
  "success": true,
  "message": "Payment skipped. Complete payment later.",
  "data": {
    "user": {
      "userId": "USR_123456",
      "status": "pending_payment",
      "isPaid": false
    },
    "nextStep": "registration_complete_offline_payment_pending"
  }
}
```

---

### **Scenario 3: Bank Transfer with Payment Proof Upload**

#### Endpoint: `POST /api/register/pay`
```bash
# Create a test image file first
echo "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==" | base64 -d > test_receipt.png

curl -X POST http://localhost:5000/api/register/pay \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -F "planType=monthly" \
  -F "paymentMethod=Bank Transfer" \
  -F "payerName=Ahmed Hassan" \
  -F "payerPhone=+************" \
  -F "notes=Bank transfer to DeynCare account" \
  -F "bankDetails=Salaam Bank - Account #*********" \
  -F "transferReference=TXN20241216001" \
  -F "paymentProof=@test_receipt.png"
```

#### Expected Response:
```json
{
  "success": true,
  "message": "Payment skipped. Complete payment later.",
  "data": {
    "user": {
      "userId": "USR_123456",
      "status": "pending_payment",
      "isPaid": false
    },
    "shop": {
      "shopId": "SHP_789012",
      "status": "pending"
    },
    "subscription": {
      "subscriptionId": "SUB_345678",
      "status": "pending_payment",
      "payment": {
        "method": "Bank Transfer",
        "verified": false,
        "paymentDetails": {
          "payerName": "Ahmed Hassan",
          "payerPhone": "+************",
          "notes": "Bank transfer to DeynCare account",
          "bankDetails": "Salaam Bank - Account #*********",
          "transferReference": "TXN20241216001",
          "paymentProof": {
            "filename": "SHP_789012_subscription_1703596801234.png",
            "originalName": "test_receipt.png",
            "size": 95
          },
          "submissionMethod": "enhanced_offline"
        }
      }
    },
    "nextStep": "registration_complete_offline_payment_pending"
  }
}
```

---

### **Scenario 4: Enhanced Offline Payment Submission (Alternative Endpoint)**

#### Endpoint: `POST /api/register/submit-offline-payment`
```bash
# Note: This endpoint may not be fully implemented based on codebase analysis
curl -X POST http://localhost:5000/api/register/submit-offline-payment \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -F "payerName=Ahmed Hassan" \
  -F "payerPhone=+************" \
  -F "notes=Paid via bank transfer to company account" \
  -F "bankDetails=Transfer to Salaam Bank - Account #*********" \
  -F "transferReference=TXN20241216001" \
  -F "paymentProof=@test_receipt.png"
```

#### Expected Response (if implemented):
```json
{
  "success": true,
  "message": "Offline payment details submitted successfully",
  "data": {
    "paymentId": "PAY_456789",
    "status": "pending_verification",
    "submissionMethod": "enhanced_offline",
    "fileId": "FILE_789012"
  }
}
```

---

### **Scenario 5: File Upload Validation Tests**

#### Test 5a: Invalid File Type
```bash
echo "This is a text file" > test_file.txt
curl -X POST http://localhost:5000/api/register/pay \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -F "planType=monthly" \
  -F "paymentMethod=Cash" \
  -F "paymentProof=@test_file.txt"
```

#### Expected Error Response:
```json
{
  "success": false,
  "message": "Invalid file type. Only JPG, PNG, and PDF files are allowed.",
  "error": {
    "code": "invalid_file_type",
    "statusCode": 400
  }
}
```

#### Test 5b: File Too Large (>5MB)
```bash
# Create a large file (>5MB)
dd if=/dev/zero of=large_file.jpg bs=1M count=6

curl -X POST http://localhost:5000/api/register/pay \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -F "planType=monthly" \
  -F "paymentMethod=Cash" \
  -F "paymentProof=@large_file.jpg"
```

#### Expected Error Response:
```json
{
  "success": false,
  "message": "File too large. Maximum file size is 5MB.",
  "error": {
    "code": "file_too_large",
    "statusCode": 400
  }
}
```

---

### **Scenario 6: Multiple Payment Methods Testing**

#### Test 6a: Check Payment Method
```bash
curl -X POST http://localhost:5000/api/register/pay \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "planType": "monthly",
    "paymentMethod": "Check",
    "notes": "Will send check via mail"
  }'
```

#### Test 6b: Other Payment Method
```bash
curl -X POST http://localhost:5000/api/register/pay \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "planType": "monthly",
    "paymentMethod": "Other",
    "notes": "Payment via mobile money transfer"
  }'
```

---

## 🚨 Error Scenarios

### **Error 1: Missing Authentication**
```bash
curl -X POST http://localhost:5000/api/register/pay \
  -H "Content-Type: application/json" \
  -d '{
    "planType": "monthly",
    "paymentMethod": "offline"
  }'
```

#### Expected Error:
```json
{
  "success": false,
  "message": "Access token is required",
  "error": {
    "code": "missing_token",
    "statusCode": 401
  }
}
```

### **Error 2: Invalid Payment Method**
```bash
curl -X POST http://localhost:5000/api/register/pay \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "planType": "monthly",
    "paymentMethod": "InvalidMethod"
  }'
```

#### Expected Error:
```json
{
  "success": false,
  "message": "Validation error",
  "error": {
    "code": "validation_error",
    "statusCode": 400,
    "details": [
      {
        "field": "paymentMethod",
        "message": "paymentMethod must be one of [EVC Plus, Card, Mobile Money, Cash, Bank Transfer, offline]"
      }
    ]
  }
}
```

---

## 📧 Email Notification Verification

After successful offline payment submission, verify that SuperAdmin receives email notification:

### Check Email Logs:
```bash
# Check application logs for email sending confirmation
tail -f logs/app.log | grep "Enhanced SuperAdmin notification sent"
```

### Expected Log Entry:
```
[INFO] Enhanced SuperAdmin notification sent for offline payment order: Ahmed Electronics (enhanced_offline)
```

---

## 🔍 Database Verification

### Check Payment Record:
```javascript
// MongoDB query to verify payment record
db.subscriptions.findOne({
  "payment.method": { $in: ["offline", "Cash", "Bank Transfer"] },
  "payment.verified": false
})
```

### Check Shop Status:
```javascript
// MongoDB query to verify shop status
db.shops.findOne({
  "shopName": "Ahmed Electronics",
  "status": "pending"
})
```

---

## 📝 Notes

1. **File Upload Limits**: Maximum 5MB, supports JPG, PNG, PDF
2. **Authentication Required**: All payment endpoints require valid JWT token
3. **Email Notifications**: SuperAdmin receives notifications for offline payments
4. **Submission Methods**: System tracks "basic_offline" vs "enhanced_offline"
5. **Payment Verification**: All offline payments require SuperAdmin approval
6. **Status Tracking**: Users remain in "pending_payment" status until approval

---

## 🎯 Success Criteria

- ✅ User can submit offline payment without file upload
- ✅ User can submit offline payment with file upload
- ✅ System validates file types and sizes
- ✅ SuperAdmin receives email notifications
- ✅ Payment records are created with correct status
- ✅ Shop remains in pending status until approval
- ✅ Error handling works for invalid inputs
