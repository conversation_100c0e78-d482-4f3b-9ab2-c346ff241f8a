-- Merging decision tree log ---
application
INJECTED from C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:14:5-92:19
INJECTED from C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\debug\AndroidManifest.xml
MERGED from [:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:13:5-46:19
MERGED from [:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:13:5-46:19
MERGED from [:firebase_analytics] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_analytics\intermediates\merged_manifest\debug\AndroidManifest.xml:11:5-17:19
MERGED from [:firebase_analytics] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_analytics\intermediates\merged_manifest\debug\AndroidManifest.xml:11:5-17:19
MERGED from [:firebase_core] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_core] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-13:19
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cc572472b49570e8646bb589993a119b\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:28:5-36:19
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cc572472b49570e8646bb589993a119b\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:28:5-36:19
MERGED from [com.google.firebase:firebase-analytics:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\fe387be9ccb4d7c849ed720f14858820\transformed\jetified-firebase-analytics-21.6.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\fe387be9ccb4d7c849ed720f14858820\transformed\jetified-firebase-analytics-21.6.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f5d009877ead73fa58d6e4160bb4830\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f5d009877ead73fa58d6e4160bb4830\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\289a5ecd1461865315d1031ce26cad65\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\289a5ecd1461865315d1031ce26cad65\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\1027ddadf767d22a0555e7d7d1604288\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\1027ddadf767d22a0555e7d7d1604288\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:8:5-16:19
MERGED from [:share_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-33:19
MERGED from [:share_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-33:19
MERGED from [:image_picker_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-32:19
MERGED from [:image_picker_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-32:19
MERGED from [:url_launcher_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-12:19
MERGED from [:url_launcher_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-12:19
MERGED from [:open_file_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-20:19
MERGED from [:open_file_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-20:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f6418bfebadcd9e0fd7d118f4b06fea4\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f6418bfebadcd9e0fd7d118f4b06fea4\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\db603b42ab4ee8471ccf00b02b70e058\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\db603b42ab4ee8471ccf00b02b70e058\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7d679a13a627326f3f4534e4c3dea4fb\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7d679a13a627326f3f4534e4c3dea4fb\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\74e9a65019ba0a30e82a78d1f7bd8a6b\transformed\jetified-play-services-measurement-sdk-21.6.1\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\74e9a65019ba0a30e82a78d1f7bd8a6b\transformed\jetified-play-services-measurement-sdk-21.6.1\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\0879a96deba1802dbda911e1e856ab70\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\0879a96deba1802dbda911e1e856ab70\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:19:5-23:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\06933728235e73d7d65b1a63335411d5\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\06933728235e73d7d65b1a63335411d5\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\309379801f705abc0a55aaad89d694ec\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\309379801f705abc0a55aaad89d694ec\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\cca8f7fd3870ece9f227d204a0c43bd1\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\cca8f7fd3870ece9f227d204a0c43bd1\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\af03bdcd696f8df9133f9358f674316b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\af03bdcd696f8df9133f9358f674316b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5d63ccd608ec6e693fb6e6a6ff4129d\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5d63ccd608ec6e693fb6e6a6ff4129d\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\9d21234604d5de133528520faad3c971\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\9d21234604d5de133528520faad3c971\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\fd77ca2e85320247f1435490519da064\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\fd77ca2e85320247f1435490519da064\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7278189eca8aa510548b82058c0b0ce3\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7278189eca8aa510548b82058c0b0ce3\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b0c24c180d00f13bfcce4251ba63970\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b0c24c180d00f13bfcce4251ba63970\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9245f76369a62d9fceec0c6b53489666\transformed\jetified-play-services-measurement-base-21.6.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9245f76369a62d9fceec0c6b53489666\transformed\jetified-play-services-measurement-base-21.6.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f411ab778a71bcd166e4feba4d61db04\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f411ab778a71bcd166e4feba4d61db04\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\9b9cc03f2bd74b7af0023cf87b6a2633\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\9b9cc03f2bd74b7af0023cf87b6a2633\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f8d9d20087cdb46136ec8d4e228ffa1\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f8d9d20087cdb46136ec8d4e228ffa1\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\d99ff18bbe6057b4e58d79e0bd3776a9\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\d99ff18bbe6057b4e58d79e0bd3776a9\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\b5106d3e1c3f02e0d77be4cf490616bd\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:23:5-31:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\b5106d3e1c3f02e0d77be4cf490616bd\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:23:5-31:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\d00ad81142e4ea506fba000731778171\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\d00ad81142e4ea506fba000731778171\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\87c6bc5d9351f73b7b7de3739170458b\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\87c6bc5d9351f73b7b7de3739170458b\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:15:5-44:19
MERGED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:15:5-44:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\9b9cc03f2bd74b7af0023cf87b6a2633\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:name
		INJECTED from C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
INJECTED from C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:22:9-34:20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\06933728235e73d7d65b1a63335411d5\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\06933728235e73d7d65b1a63335411d5\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\af03bdcd696f8df9133f9358f674316b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\af03bdcd696f8df9133f9358f674316b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f8d9d20087cdb46136ec8d4e228ffa1\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f8d9d20087cdb46136ec8d4e228ffa1\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	android:authorities
		INJECTED from C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml
provider#androidx.core.content.FileProvider
INJECTED from C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:47:9-55:20
	android:authorities
		INJECTED from C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml
manifest
ADDED from C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:2:1-104:12
MERGED from C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:2:1-104:12
INJECTED from C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [:shared_preferences_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\shared_preferences_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-48:12
MERGED from [:firebase_analytics] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_analytics\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-19:12
MERGED from [:firebase_core] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-15:12
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:17:1-66:12
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cc572472b49570e8646bb589993a119b\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:17:1-38:12
MERGED from [com.google.firebase:firebase-analytics:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\fe387be9ccb4d7c849ed720f14858820\transformed\jetified-firebase-analytics-21.6.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f5d009877ead73fa58d6e4160bb4830\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:17:1-43:12
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\289a5ecd1461865315d1031ce26cad65\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\1027ddadf767d22a0555e7d7d1604288\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:2:1-18:12
MERGED from [:device_info_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\device_info_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:network_info_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\network_info_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-10:12
MERGED from [:package_info_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\package_info_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:share_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-35:12
MERGED from [:fluttertoast] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\fluttertoast\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:rive_common] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\rive_common\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:file_picker] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-19:12
MERGED from [:flutter_local_notifications] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-10:12
MERGED from [:image_picker_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-34:12
MERGED from [:url_launcher_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-14:12
MERGED from [:app_links] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\app_links\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:connectivity_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\connectivity_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:flutter_secure_storage] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\flutter_secure_storage\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-10:12
MERGED from [:integration_test] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\integration_test\intermediates\merged_manifest\debug\AndroidManifest.xml:7:1-12:12
MERGED from [:open_file_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-22:12
MERGED from [:path_provider_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\path_provider_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:permission_handler_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\permission_handler_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:sqflite_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\sqflite_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.media:media:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\82c8c18125dbb1850e8e168081886362\transformed\media-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\bab33645c7cc5071f3d34874362f13b8\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\20bc7ec7ebe67f5fcea4390319061ea3\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f08271f00e0d91f86d00dd1fe7c256d5\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\f4a5c5182800a815e5bd1555d17660a5\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f6418bfebadcd9e0fd7d118f4b06fea4\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\db603b42ab4ee8471ccf00b02b70e058\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7d679a13a627326f3f4534e4c3dea4fb\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\74e9a65019ba0a30e82a78d1f7bd8a6b\transformed\jetified-play-services-measurement-sdk-21.6.1\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b69fb7466897aec0ac0b824877ad1ba8\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\0879a96deba1802dbda911e1e856ab70\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3a4e6125bf940b739477aceb5c987912\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6615525e1384e5325b031cddb794e21a\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\06933728235e73d7d65b1a63335411d5\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\transforms-3\2a57e7bae530a4c78f618077ab72862f\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\309379801f705abc0a55aaad89d694ec\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\cca8f7fd3870ece9f227d204a0c43bd1\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8564d405d92431eb77739c1afe17b7a8\transformed\recyclerview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\820ffe8d9f3ec51c9085b6a5269aa2f8\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b61ac209d87aa7ed76b6ea5e5b8b4a38\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d228d58873541fdb86accfe1dd7300b\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\e3e51bbd5594eb928e49cd67daf1a864\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d216c3126d19dd547e00d597dc9fdb3b\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a03c496ef296c5c562e7fbf6bfa27bf6\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\53c546102ac42cc162e6279d4bba7217\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\1c7f6a3b8c9f69d00f33f8bb9dc01bc4\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c0f176393bf19427ff7f68df32781a51\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c82220a367aaeedb30368b9aea12bb9d\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\af03bdcd696f8df9133f9358f674316b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9413e6fa06daca75373f4ffd7792aec2\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\f4cc015161212d3cb5e18459a73decb2\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\6ad53696758cdf1e441f573f70eabdae\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d6bf07629c7787434a75b27bb833929d\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:17:1-21:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4c58160b4cc326da1f61860d9213af8f\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5d63ccd608ec6e693fb6e6a6ff4129d\transformed\jetified-window-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\303b4fcfd7ca4ba9e379e020765d82cb\transformed\jetified-datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\7d38a7577b7c67eb438496b3f8263f18\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\e20bbd588f2e10629e9b18ac7d11b441\transformed\jetified-datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\c9a8c9f3f013b17d47a677fc08055dad\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\9d21234604d5de133528520faad3c971\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:17:1-28:12
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\fd77ca2e85320247f1435490519da064\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7278189eca8aa510548b82058c0b0ce3\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b0c24c180d00f13bfcce4251ba63970\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\bbc5b25862772c7ca9c6d5da0ee20505\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9245f76369a62d9fceec0c6b53489666\transformed\jetified-play-services-measurement-base-21.6.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f411ab778a71bcd166e4feba4d61db04\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\a0d017cdd0a1d3dd417e3114af803c10\transformed\fragment-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\transforms-3\6e355c2667d565769ffda304a9d2ab64\transformed\jetified-activity-1.9.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\69724ef64d89ca5c07ee06d94795dda1\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\892f6c79e1af29d0bfd47034b0921ea4\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\57cb935353a8cae3d4b71348efb6c3ef\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c73079d60235b6fbd94601cda2e2705c\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3ce5cdbaca83950752a0ff6e39fab4f2\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\909b945fd51a6ff9d023ad5775f3cbf7\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fe34b98a2e26a79b651c4ea4bfaa209c\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\4abb12185c86b107e63ab61c14b79fe5\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\73ef12cc3c330594613ead46ae70814a\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cdfba69eabd6e949dcc2c15352249a1c\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\34c99dba9ed17f3897a8de280cb5ff37\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\9b9cc03f2bd74b7af0023cf87b6a2633\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\dabf98eb86149448a6ebe173ad67ebb9\transformed\localbroadcastmanager-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\transforms-3\6abff3330e700c1abac1d2095eff2fe6\transformed\jetified-security-crypto-1.1.0-alpha06\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\transforms-3\93327a02a7c7a41d0a9575c0bfa56caf\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.test:rules:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\82edee5040aedd3d737a9dfde9b69d0f\transformed\rules-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.test.espresso:espresso-core:3.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\331504081f633f823a0a7834d7769ef8\transformed\espresso-core-3.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.test:runner:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\bfdd60e16ecae56c274f32972d3c981a\transformed\runner-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f8d9d20087cdb46136ec8d4e228ffa1\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\bc70f24dba4c4a25c11b20c72f8d1c14\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\d99ff18bbe6057b4e58d79e0bd3776a9\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\86c059a52203c46c8c0520dd630bbde4\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.test:monitor:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ca8fa3d91476d6cce00678f681b2d7be\transformed\monitor-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f863ecbc7001b47a3771452e9ccd272e\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8060df56af1b1e0a5737dbdda93e03ab\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\transforms-3\4c19f27c99709720b3c331d2f987890a\transformed\jetified-firebase-components-17.1.5\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\b5106d3e1c3f02e0d77be4cf490616bd\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:15:1-33:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\d00ad81142e4ea506fba000731778171\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\87c6bc5d9351f73b7b7de3739170458b\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8d13361724ece9d5dff7d6db198316f2\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9dd6c3cd6569aa78c544d2a527181920\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\841d216a0bc632643685b810794e4ff2\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c0f60c4ad9ccf562fa35451a8aeb9b04\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d0a0834fb484e5acbb6f90b70df4e84f\transformed\jetified-core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\24494a3659eb3308e8d6937d184263d5\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:2:1-46:12
MERGED from [org.microg:safe-parcel:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\b3ce4e2ecb015b08bea9e060f885eb92\transformed\jetified-safe-parcel-1.7.0\AndroidManifest.xml:6:1-14:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\transforms-3\5c63d9b19992194f4b2546b54bfec00e\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.test.espresso:espresso-idling-resource:3.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\216e5ac44124abb78c0d7fc2f37222cf\transformed\espresso-idling-resource-3.2.0\AndroidManifest.xml:17:1-24:12
	package
		INJECTED from C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\debug\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:3:5-51
		ADDED from C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:5:5-67
MERGED from C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:5:5-67
MERGED from C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:5:5-67
MERGED from [:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-67
MERGED from [:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-67
MERGED from [:firebase_analytics] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_analytics\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-67
MERGED from [:firebase_analytics] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_analytics\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cc572472b49570e8646bb589993a119b\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cc572472b49570e8646bb589993a119b\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f5d009877ead73fa58d6e4160bb4830\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f5d009877ead73fa58d6e4160bb4830\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:22:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\289a5ecd1461865315d1031ce26cad65\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\289a5ecd1461865315d1031ce26cad65\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7d679a13a627326f3f4534e4c3dea4fb\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7d679a13a627326f3f4534e4c3dea4fb\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b69fb7466897aec0ac0b824877ad1ba8\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b69fb7466897aec0ac0b824877ad1ba8\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\309379801f705abc0a55aaad89d694ec\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\309379801f705abc0a55aaad89d694ec\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\bbc5b25862772c7ca9c6d5da0ee20505\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\bbc5b25862772c7ca9c6d5da0ee20505\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\d00ad81142e4ea506fba000731778171\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\d00ad81142e4ea506fba000731778171\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:25:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:7:5-79
MERGED from [:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-79
MERGED from [:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-79
MERGED from [:firebase_analytics] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_analytics\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-79
MERGED from [:firebase_analytics] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_analytics\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cc572472b49570e8646bb589993a119b\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cc572472b49570e8646bb589993a119b\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f5d009877ead73fa58d6e4160bb4830\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f5d009877ead73fa58d6e4160bb4830\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\289a5ecd1461865315d1031ce26cad65\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\289a5ecd1461865315d1031ce26cad65\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [:network_info_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\network_info_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-79
MERGED from [:network_info_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\network_info_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-79
MERGED from [:connectivity_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\connectivity_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-79
MERGED from [:connectivity_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\connectivity_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7d679a13a627326f3f4534e4c3dea4fb\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7d679a13a627326f3f4534e4c3dea4fb\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b69fb7466897aec0ac0b824877ad1ba8\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b69fb7466897aec0ac0b824877ad1ba8\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\309379801f705abc0a55aaad89d694ec\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\309379801f705abc0a55aaad89d694ec\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\bbc5b25862772c7ca9c6d5da0ee20505\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\bbc5b25862772c7ca9c6d5da0ee20505\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\d00ad81142e4ea506fba000731778171\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\d00ad81142e4ea506fba000731778171\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\87c6bc5d9351f73b7b7de3739170458b\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\87c6bc5d9351f73b7b7de3739170458b\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:22:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:10:5-81
	android:name
		ADDED from C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:10:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:11:5-80
MERGED from [:file_picker] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-9:38
MERGED from [:file_picker] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-9:38
	android:maxSdkVersion
		ADDED from [:file_picker] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:9:9-35
	android:name
		ADDED from C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:11:22-77
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:13:5-111
	tools:ignore
		ADDED from C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:13:80-108
	android:name
		ADDED from C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:13:22-79
queries
ADDED from C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:98:5-103:15
MERGED from [:file_picker] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:11:5-17:15
MERGED from [:file_picker] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:11:5-17:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:99:9-102:18
action#android.intent.action.PROCESS_TEXT
ADDED from C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:100:13-72
	android:name
		ADDED from C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:100:21-70
data
ADDED from C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:101:13-50
	android:mimeType
		ADDED from C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\main\AndroidManifest.xml:101:19-48
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\debug\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\debug\AndroidManifest.xml
MERGED from [:shared_preferences_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\shared_preferences_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\shared_preferences_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:firebase_analytics] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_analytics\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:firebase_analytics] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_analytics\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cc572472b49570e8646bb589993a119b\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cc572472b49570e8646bb589993a119b\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-analytics:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\fe387be9ccb4d7c849ed720f14858820\transformed\jetified-firebase-analytics-21.6.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\fe387be9ccb4d7c849ed720f14858820\transformed\jetified-firebase-analytics-21.6.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f5d009877ead73fa58d6e4160bb4830\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f5d009877ead73fa58d6e4160bb4830\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\289a5ecd1461865315d1031ce26cad65\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\289a5ecd1461865315d1031ce26cad65\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\1027ddadf767d22a0555e7d7d1604288\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\1027ddadf767d22a0555e7d7d1604288\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:5:5-44
MERGED from [:device_info_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\device_info_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:device_info_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\device_info_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:network_info_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\network_info_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:network_info_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\network_info_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:package_info_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\package_info_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:package_info_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\package_info_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:share_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:share_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:fluttertoast] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\fluttertoast\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:fluttertoast] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\fluttertoast\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:rive_common] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\rive_common\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:rive_common] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\rive_common\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:file_picker] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:file_picker] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:flutter_local_notifications] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:flutter_local_notifications] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:image_picker_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:6:5-44
MERGED from [:image_picker_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:6:5-44
MERGED from [:url_launcher_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:app_links] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\app_links\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:app_links] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\app_links\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:connectivity_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\connectivity_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:connectivity_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\connectivity_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:flutter_secure_storage] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\flutter_secure_storage\intermediates\merged_manifest\debug\AndroidManifest.xml:6:5-8:53
MERGED from [:flutter_secure_storage] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\flutter_secure_storage\intermediates\merged_manifest\debug\AndroidManifest.xml:6:5-8:53
MERGED from [:integration_test] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\integration_test\intermediates\merged_manifest\debug\AndroidManifest.xml:10:5-44
MERGED from [:integration_test] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\integration_test\intermediates\merged_manifest\debug\AndroidManifest.xml:10:5-44
MERGED from [:open_file_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:6:5-44
MERGED from [:open_file_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:6:5-44
MERGED from [:path_provider_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\path_provider_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\path_provider_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\permission_handler_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\permission_handler_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\sqflite_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\sqflite_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\82c8c18125dbb1850e8e168081886362\transformed\media-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\82c8c18125dbb1850e8e168081886362\transformed\media-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\bab33645c7cc5071f3d34874362f13b8\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\bab33645c7cc5071f3d34874362f13b8\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\20bc7ec7ebe67f5fcea4390319061ea3\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\20bc7ec7ebe67f5fcea4390319061ea3\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f08271f00e0d91f86d00dd1fe7c256d5\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f08271f00e0d91f86d00dd1fe7c256d5\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\f4a5c5182800a815e5bd1555d17660a5\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\f4a5c5182800a815e5bd1555d17660a5\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f6418bfebadcd9e0fd7d118f4b06fea4\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f6418bfebadcd9e0fd7d118f4b06fea4\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\db603b42ab4ee8471ccf00b02b70e058\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\db603b42ab4ee8471ccf00b02b70e058\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:19:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7d679a13a627326f3f4534e4c3dea4fb\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7d679a13a627326f3f4534e4c3dea4fb\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\74e9a65019ba0a30e82a78d1f7bd8a6b\transformed\jetified-play-services-measurement-sdk-21.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\74e9a65019ba0a30e82a78d1f7bd8a6b\transformed\jetified-play-services-measurement-sdk-21.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b69fb7466897aec0ac0b824877ad1ba8\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b69fb7466897aec0ac0b824877ad1ba8\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\0879a96deba1802dbda911e1e856ab70\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\0879a96deba1802dbda911e1e856ab70\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:18:5-43
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3a4e6125bf940b739477aceb5c987912\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3a4e6125bf940b739477aceb5c987912\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6615525e1384e5325b031cddb794e21a\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6615525e1384e5325b031cddb794e21a\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\06933728235e73d7d65b1a63335411d5\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\06933728235e73d7d65b1a63335411d5\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\transforms-3\2a57e7bae530a4c78f618077ab72862f\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\transforms-3\2a57e7bae530a4c78f618077ab72862f\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\309379801f705abc0a55aaad89d694ec\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\309379801f705abc0a55aaad89d694ec\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\cca8f7fd3870ece9f227d204a0c43bd1\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\cca8f7fd3870ece9f227d204a0c43bd1\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8564d405d92431eb77739c1afe17b7a8\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8564d405d92431eb77739c1afe17b7a8\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\820ffe8d9f3ec51c9085b6a5269aa2f8\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\820ffe8d9f3ec51c9085b6a5269aa2f8\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b61ac209d87aa7ed76b6ea5e5b8b4a38\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b61ac209d87aa7ed76b6ea5e5b8b4a38\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d228d58873541fdb86accfe1dd7300b\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d228d58873541fdb86accfe1dd7300b\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\e3e51bbd5594eb928e49cd67daf1a864\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\e3e51bbd5594eb928e49cd67daf1a864\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d216c3126d19dd547e00d597dc9fdb3b\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d216c3126d19dd547e00d597dc9fdb3b\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a03c496ef296c5c562e7fbf6bfa27bf6\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a03c496ef296c5c562e7fbf6bfa27bf6\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\53c546102ac42cc162e6279d4bba7217\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\53c546102ac42cc162e6279d4bba7217\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\1c7f6a3b8c9f69d00f33f8bb9dc01bc4\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\1c7f6a3b8c9f69d00f33f8bb9dc01bc4\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c0f176393bf19427ff7f68df32781a51\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c0f176393bf19427ff7f68df32781a51\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c82220a367aaeedb30368b9aea12bb9d\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c82220a367aaeedb30368b9aea12bb9d\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\af03bdcd696f8df9133f9358f674316b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\af03bdcd696f8df9133f9358f674316b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9413e6fa06daca75373f4ffd7792aec2\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9413e6fa06daca75373f4ffd7792aec2\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\f4cc015161212d3cb5e18459a73decb2\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\f4cc015161212d3cb5e18459a73decb2\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\6ad53696758cdf1e441f573f70eabdae\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\6ad53696758cdf1e441f573f70eabdae\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d6bf07629c7787434a75b27bb833929d\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d6bf07629c7787434a75b27bb833929d\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4c58160b4cc326da1f61860d9213af8f\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4c58160b4cc326da1f61860d9213af8f\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5d63ccd608ec6e693fb6e6a6ff4129d\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5d63ccd608ec6e693fb6e6a6ff4129d\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\303b4fcfd7ca4ba9e379e020765d82cb\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\303b4fcfd7ca4ba9e379e020765d82cb\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\7d38a7577b7c67eb438496b3f8263f18\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\7d38a7577b7c67eb438496b3f8263f18\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\e20bbd588f2e10629e9b18ac7d11b441\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\e20bbd588f2e10629e9b18ac7d11b441\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\c9a8c9f3f013b17d47a677fc08055dad\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\c9a8c9f3f013b17d47a677fc08055dad\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\9d21234604d5de133528520faad3c971\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\9d21234604d5de133528520faad3c971\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\fd77ca2e85320247f1435490519da064\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\fd77ca2e85320247f1435490519da064\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7278189eca8aa510548b82058c0b0ce3\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7278189eca8aa510548b82058c0b0ce3\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b0c24c180d00f13bfcce4251ba63970\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b0c24c180d00f13bfcce4251ba63970\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\bbc5b25862772c7ca9c6d5da0ee20505\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\bbc5b25862772c7ca9c6d5da0ee20505\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9245f76369a62d9fceec0c6b53489666\transformed\jetified-play-services-measurement-base-21.6.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9245f76369a62d9fceec0c6b53489666\transformed\jetified-play-services-measurement-base-21.6.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f411ab778a71bcd166e4feba4d61db04\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f411ab778a71bcd166e4feba4d61db04\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\a0d017cdd0a1d3dd417e3114af803c10\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\a0d017cdd0a1d3dd417e3114af803c10\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\transforms-3\6e355c2667d565769ffda304a9d2ab64\transformed\jetified-activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\transforms-3\6e355c2667d565769ffda304a9d2ab64\transformed\jetified-activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\69724ef64d89ca5c07ee06d94795dda1\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\69724ef64d89ca5c07ee06d94795dda1\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\892f6c79e1af29d0bfd47034b0921ea4\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\892f6c79e1af29d0bfd47034b0921ea4\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\57cb935353a8cae3d4b71348efb6c3ef\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\57cb935353a8cae3d4b71348efb6c3ef\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c73079d60235b6fbd94601cda2e2705c\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c73079d60235b6fbd94601cda2e2705c\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3ce5cdbaca83950752a0ff6e39fab4f2\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3ce5cdbaca83950752a0ff6e39fab4f2\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\909b945fd51a6ff9d023ad5775f3cbf7\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\909b945fd51a6ff9d023ad5775f3cbf7\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fe34b98a2e26a79b651c4ea4bfaa209c\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fe34b98a2e26a79b651c4ea4bfaa209c\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\4abb12185c86b107e63ab61c14b79fe5\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\4abb12185c86b107e63ab61c14b79fe5\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\73ef12cc3c330594613ead46ae70814a\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\73ef12cc3c330594613ead46ae70814a\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cdfba69eabd6e949dcc2c15352249a1c\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cdfba69eabd6e949dcc2c15352249a1c\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\34c99dba9ed17f3897a8de280cb5ff37\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\34c99dba9ed17f3897a8de280cb5ff37\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\9b9cc03f2bd74b7af0023cf87b6a2633\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\9b9cc03f2bd74b7af0023cf87b6a2633\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\dabf98eb86149448a6ebe173ad67ebb9\transformed\localbroadcastmanager-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\dabf98eb86149448a6ebe173ad67ebb9\transformed\localbroadcastmanager-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\transforms-3\6abff3330e700c1abac1d2095eff2fe6\transformed\jetified-security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\transforms-3\6abff3330e700c1abac1d2095eff2fe6\transformed\jetified-security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\transforms-3\93327a02a7c7a41d0a9575c0bfa56caf\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\transforms-3\93327a02a7c7a41d0a9575c0bfa56caf\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:rules:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\82edee5040aedd3d737a9dfde9b69d0f\transformed\rules-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:rules:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\82edee5040aedd3d737a9dfde9b69d0f\transformed\rules-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test.espresso:espresso-core:3.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\331504081f633f823a0a7834d7769ef8\transformed\espresso-core-3.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test.espresso:espresso-core:3.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\331504081f633f823a0a7834d7769ef8\transformed\espresso-core-3.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:runner:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\bfdd60e16ecae56c274f32972d3c981a\transformed\runner-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:runner:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\bfdd60e16ecae56c274f32972d3c981a\transformed\runner-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f8d9d20087cdb46136ec8d4e228ffa1\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f8d9d20087cdb46136ec8d4e228ffa1\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\bc70f24dba4c4a25c11b20c72f8d1c14\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\bc70f24dba4c4a25c11b20c72f8d1c14\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\d99ff18bbe6057b4e58d79e0bd3776a9\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\d99ff18bbe6057b4e58d79e0bd3776a9\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\86c059a52203c46c8c0520dd630bbde4\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\86c059a52203c46c8c0520dd630bbde4\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.test:monitor:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ca8fa3d91476d6cce00678f681b2d7be\transformed\monitor-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:monitor:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ca8fa3d91476d6cce00678f681b2d7be\transformed\monitor-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f863ecbc7001b47a3771452e9ccd272e\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f863ecbc7001b47a3771452e9ccd272e\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8060df56af1b1e0a5737dbdda93e03ab\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8060df56af1b1e0a5737dbdda93e03ab\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\transforms-3\4c19f27c99709720b3c331d2f987890a\transformed\jetified-firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\transforms-3\4c19f27c99709720b3c331d2f987890a\transformed\jetified-firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\b5106d3e1c3f02e0d77be4cf490616bd\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\b5106d3e1c3f02e0d77be4cf490616bd\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\d00ad81142e4ea506fba000731778171\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\d00ad81142e4ea506fba000731778171\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\87c6bc5d9351f73b7b7de3739170458b\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\87c6bc5d9351f73b7b7de3739170458b\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8d13361724ece9d5dff7d6db198316f2\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8d13361724ece9d5dff7d6db198316f2\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9dd6c3cd6569aa78c544d2a527181920\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9dd6c3cd6569aa78c544d2a527181920\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\841d216a0bc632643685b810794e4ff2\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\841d216a0bc632643685b810794e4ff2\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c0f60c4ad9ccf562fa35451a8aeb9b04\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c0f60c4ad9ccf562fa35451a8aeb9b04\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d0a0834fb484e5acbb6f90b70df4e84f\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d0a0834fb484e5acbb6f90b70df4e84f\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\24494a3659eb3308e8d6937d184263d5\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\24494a3659eb3308e8d6937d184263d5\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:7:5-9:41
MERGED from [org.microg:safe-parcel:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\b3ce4e2ecb015b08bea9e060f885eb92\transformed\jetified-safe-parcel-1.7.0\AndroidManifest.xml:10:5-12:41
MERGED from [org.microg:safe-parcel:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\b3ce4e2ecb015b08bea9e060f885eb92\transformed\jetified-safe-parcel-1.7.0\AndroidManifest.xml:10:5-12:41
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\transforms-3\5c63d9b19992194f4b2546b54bfec00e\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\transforms-3\5c63d9b19992194f4b2546b54bfec00e\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [androidx.test.espresso:espresso-idling-resource:3.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\216e5ac44124abb78c0d7fc2f37222cf\transformed\espresso-idling-resource-3.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test.espresso:espresso-idling-resource:3.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\216e5ac44124abb78c0d7fc2f37222cf\transformed\espresso-idling-resource-3.2.0\AndroidManifest.xml:20:5-22:41
	tools:overrideLibrary
		ADDED from [:flutter_secure_storage] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\flutter_secure_storage\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-50
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\android\app\src\debug\AndroidManifest.xml
uses-permission#android.permission.WAKE_LOCK
ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-68
MERGED from [:firebase_analytics] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_analytics\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-68
MERGED from [:firebase_analytics] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_analytics\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-68
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cc572472b49570e8646bb589993a119b\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cc572472b49570e8646bb589993a119b\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f5d009877ead73fa58d6e4160bb4830\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f5d009877ead73fa58d6e4160bb4830\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7d679a13a627326f3f4534e4c3dea4fb\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7d679a13a627326f3f4534e4c3dea4fb\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b69fb7466897aec0ac0b824877ad1ba8\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b69fb7466897aec0ac0b824877ad1ba8\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\309379801f705abc0a55aaad89d694ec\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\309379801f705abc0a55aaad89d694ec\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\bbc5b25862772c7ca9c6d5da0ee20505\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\bbc5b25862772c7ca9c6d5da0ee20505\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:25:5-68
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:8:22-65
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:11:5-77
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:23:5-77
MERGED from [:flutter_local_notifications] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-77
MERGED from [:flutter_local_notifications] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-77
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:11:22-74
service#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService
ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:14:9-17:72
	android:exported
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-37
	android:permission
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:17:13-69
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:15:13-107
service#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService
ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:18:9-24:19
	android:exported
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:20:13-37
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:19:13-97
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:21:13-23:29
action#com.google.firebase.MESSAGING_EVENT
ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:22:17-78
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:22:25-75
receiver#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver
ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:26:9-33:20
	android:exported
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:28:13-36
	android:permission
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:29:13-73
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:27:13-98
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:30:13-32:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:31:17-81
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:31:25-78
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:35:9-39:19
MERGED from [:firebase_analytics] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_analytics\intermediates\merged_manifest\debug\AndroidManifest.xml:12:9-16:19
MERGED from [:firebase_analytics] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_analytics\intermediates\merged_manifest\debug\AndroidManifest.xml:12:9-16:19
MERGED from [:firebase_core] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_core] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:19
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cc572472b49570e8646bb589993a119b\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:29:9-35:19
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cc572472b49570e8646bb589993a119b\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:29:9-35:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f5d009877ead73fa58d6e4160bb4830\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f5d009877ead73fa58d6e4160bb4830\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\289a5ecd1461865315d1031ce26cad65\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\289a5ecd1461865315d1031ce26cad65\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\1027ddadf767d22a0555e7d7d1604288\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\1027ddadf767d22a0555e7d7d1604288\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\db603b42ab4ee8471ccf00b02b70e058\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\db603b42ab4ee8471ccf00b02b70e058\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\b5106d3e1c3f02e0d77be4cf490616bd\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:24:9-30:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\b5106d3e1c3f02e0d77be4cf490616bd\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:24:9-30:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:56:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\db603b42ab4ee8471ccf00b02b70e058\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\db603b42ab4ee8471ccf00b02b70e058\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:35:18-89
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar
ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:36:13-38:85
	android:value
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:38:17-82
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:37:17-128
provider#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider
ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:41:9-45:38
	android:authorities
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:43:13-88
	android:exported
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:44:13-37
	android:initOrder
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:45:13-35
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:42:13-102
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.analytics.FlutterFirebaseAppRegistrar
ADDED from [:firebase_analytics] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_analytics\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-15:85
	android:value
		ADDED from [:firebase_analytics] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_analytics\intermediates\merged_manifest\debug\AndroidManifest.xml:15:17-82
	android:name
		ADDED from [:firebase_analytics] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_analytics\intermediates\merged_manifest\debug\AndroidManifest.xml:14:17-128
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar
ADDED from [:firebase_core] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_core] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_core] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-124
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:26:5-82
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cc572472b49570e8646bb589993a119b\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:26:5-82
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cc572472b49570e8646bb589993a119b\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b69fb7466897aec0ac0b824877ad1ba8\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b69fb7466897aec0ac0b824877ad1ba8\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:26:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:30:13-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:47:13-82
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ef6bc1433174ac2adc4872aec7e23e3\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:61:17-119
meta-data#com.google.firebase.components:com.google.firebase.iid.Registrar
ADDED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cc572472b49570e8646bb589993a119b\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:32:13-34:85
	android:value
		ADDED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cc572472b49570e8646bb589993a119b\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:34:17-82
	android:name
		ADDED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cc572472b49570e8646bb589993a119b\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:33:17-96
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f5d009877ead73fa58d6e4160bb4830\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\309379801f705abc0a55aaad89d694ec\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\309379801f705abc0a55aaad89d694ec\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b0c24c180d00f13bfcce4251ba63970\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b0c24c180d00f13bfcce4251ba63970\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\bbc5b25862772c7ca9c6d5da0ee20505\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\bbc5b25862772c7ca9c6d5da0ee20505\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f5d009877ead73fa58d6e4160bb4830\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:25:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f5d009877ead73fa58d6e4160bb4830\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\bbc5b25862772c7ca9c6d5da0ee20505\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\bbc5b25862772c7ca9c6d5da0ee20505\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f5d009877ead73fa58d6e4160bb4830\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:26:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f5d009877ead73fa58d6e4160bb4830\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\bbc5b25862772c7ca9c6d5da0ee20505\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\bbc5b25862772c7ca9c6d5da0ee20505\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f5d009877ead73fa58d6e4160bb4830\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:27:22-79
property
ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f5d009877ead73fa58d6e4160bb4830\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:30:9-32:61
	android:resource
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f5d009877ead73fa58d6e4160bb4830\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:32:13-58
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f5d009877ead73fa58d6e4160bb4830\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:31:13-65
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f5d009877ead73fa58d6e4160bb4830\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:37:13-39:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f5d009877ead73fa58d6e4160bb4830\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:39:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f5d009877ead73fa58d6e4160bb4830\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:38:17-139
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\289a5ecd1461865315d1031ce26cad65\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\289a5ecd1461865315d1031ce26cad65\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\289a5ecd1461865315d1031ce26cad65\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\289a5ecd1461865315d1031ce26cad65\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\289a5ecd1461865315d1031ce26cad65\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\289a5ecd1461865315d1031ce26cad65\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\1027ddadf767d22a0555e7d7d1604288\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\1027ddadf767d22a0555e7d7d1604288\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\1027ddadf767d22a0555e7d7d1604288\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:13:17-116
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from [:network_info_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\network_info_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-76
	android:name
		ADDED from [:network_info_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\network_info_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:8:22-73
provider#dev.fluttercommunity.plus.share.ShareFileProvider
ADDED from [:share_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:13:9-21:20
	android:grantUriPermissions
		ADDED from [:share_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:17:13-47
	android:authorities
		ADDED from [:share_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:15:13-74
	android:exported
		ADDED from [:share_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-37
	android:name
		ADDED from [:share_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-77
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:share_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:18:13-20:68
	android:resource
		ADDED from [:share_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:20:17-65
	android:name
		ADDED from [:share_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:19:17-67
receiver#dev.fluttercommunity.plus.share.SharePlusPendingIntent
ADDED from [:share_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:26:9-32:20
	android:exported
		ADDED from [:share_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [:share_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:27:13-82
intent-filter#action:name:EXTRA_CHOSEN_COMPONENT
ADDED from [:share_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:29:13-31:29
action#EXTRA_CHOSEN_COMPONENT
ADDED from [:share_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:30:17-65
	android:name
		ADDED from [:share_plus] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:30:25-62
intent#action:name:android.intent.action.GET_CONTENT+data:mimeType:*/*
ADDED from [:file_picker] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:12:9-16:18
action#android.intent.action.GET_CONTENT
ADDED from [:file_picker] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-72
	android:name
		ADDED from [:file_picker] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:13:21-69
uses-permission#android.permission.VIBRATE
ADDED from [:flutter_local_notifications] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-66
	android:name
		ADDED from [:flutter_local_notifications] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:7:22-63
provider#io.flutter.plugins.imagepicker.ImagePickerFileProvider
ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:9-17:20
	android:grantUriPermissions
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-47
	android:authorities
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-74
	android:exported
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-82
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:19:9-31:19
	android:enabled
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:21:13-36
	android:exported
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:22:13-37
	tools:ignore
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:23:13-40
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:20:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:24:13-26:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:25:17-94
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:25:25-91
meta-data#photopicker_activity:0:required
ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:28:13-30:36
	android:value
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:30:17-33
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:29:17-63
activity#io.flutter.plugins.urllauncher.WebViewActivity
ADDED from [:url_launcher_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-11:74
	android:exported
		ADDED from [:url_launcher_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [:url_launcher_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-71
	android:name
		ADDED from [:url_launcher_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-74
provider#com.crazecoder.openfile.FileProvider
ADDED from [:open_file_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:9-19:20
	android:requestLegacyExternalStorage
		ADDED from [:open_file_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-56
	android:grantUriPermissions
		ADDED from [:open_file_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-47
	android:authorities
		ADDED from [:open_file_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-88
	android:exported
		ADDED from [:open_file_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-37
	tools:replace
		ADDED from [:open_file_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:15:13-48
	android:name
		ADDED from [:open_file_android] C:\Users\<USER>\Desktop\Deyncare-mobile\deyncare_app\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-64
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\db603b42ab4ee8471ccf00b02b70e058\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\db603b42ab4ee8471ccf00b02b70e058\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\db603b42ab4ee8471ccf00b02b70e058\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\db603b42ab4ee8471ccf00b02b70e058\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\db603b42ab4ee8471ccf00b02b70e058\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\db603b42ab4ee8471ccf00b02b70e058\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\db603b42ab4ee8471ccf00b02b70e058\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\db603b42ab4ee8471ccf00b02b70e058\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\db603b42ab4ee8471ccf00b02b70e058\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:36:17-109
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7d679a13a627326f3f4534e4c3dea4fb\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\309379801f705abc0a55aaad89d694ec\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\309379801f705abc0a55aaad89d694ec\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7d679a13a627326f3f4534e4c3dea4fb\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7d679a13a627326f3f4534e4c3dea4fb\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7d679a13a627326f3f4534e4c3dea4fb\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7d679a13a627326f3f4534e4c3dea4fb\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7d679a13a627326f3f4534e4c3dea4fb\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7d679a13a627326f3f4534e4c3dea4fb\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7d679a13a627326f3f4534e4c3dea4fb\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7d679a13a627326f3f4534e4c3dea4fb\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7d679a13a627326f3f4534e4c3dea4fb\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7d679a13a627326f3f4534e4c3dea4fb\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7d679a13a627326f3f4534e4c3dea4fb\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7d679a13a627326f3f4534e4c3dea4fb\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7d679a13a627326f3f4534e4c3dea4fb\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7d679a13a627326f3f4534e4c3dea4fb\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:40:13-87
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\0879a96deba1802dbda911e1e856ab70\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\0879a96deba1802dbda911e1e856ab70\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\0879a96deba1802dbda911e1e856ab70\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\0879a96deba1802dbda911e1e856ab70\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:19-85
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\06933728235e73d7d65b1a63335411d5\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\06933728235e73d7d65b1a63335411d5\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\06933728235e73d7d65b1a63335411d5\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\af03bdcd696f8df9133f9358f674316b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\af03bdcd696f8df9133f9358f674316b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\af03bdcd696f8df9133f9358f674316b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5d63ccd608ec6e693fb6e6a6ff4129d\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5d63ccd608ec6e693fb6e6a6ff4129d\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5d63ccd608ec6e693fb6e6a6ff4129d\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5d63ccd608ec6e693fb6e6a6ff4129d\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5d63ccd608ec6e693fb6e6a6ff4129d\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5d63ccd608ec6e693fb6e6a6ff4129d\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\9d21234604d5de133528520faad3c971\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\9d21234604d5de133528520faad3c971\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\9d21234604d5de133528520faad3c971\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f411ab778a71bcd166e4feba4d61db04\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f411ab778a71bcd166e4feba4d61db04\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f411ab778a71bcd166e4feba4d61db04\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\9b9cc03f2bd74b7af0023cf87b6a2633\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\9b9cc03f2bd74b7af0023cf87b6a2633\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\9b9cc03f2bd74b7af0023cf87b6a2633\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.deyncare.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\9b9cc03f2bd74b7af0023cf87b6a2633\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\9b9cc03f2bd74b7af0023cf87b6a2633\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\9b9cc03f2bd74b7af0023cf87b6a2633\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\9b9cc03f2bd74b7af0023cf87b6a2633\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\9b9cc03f2bd74b7af0023cf87b6a2633\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.deyncare.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\9b9cc03f2bd74b7af0023cf87b6a2633\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\9b9cc03f2bd74b7af0023cf87b6a2633\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\857e12b1a7c264079b8b7152d58ca770\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\b5106d3e1c3f02e0d77be4cf490616bd\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\b5106d3e1c3f02e0d77be4cf490616bd\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\b5106d3e1c3f02e0d77be4cf490616bd\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\d00ad81142e4ea506fba000731778171\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\87c6bc5d9351f73b7b7de3739170458b\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\87c6bc5d9351f73b7b7de3739170458b\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\d00ad81142e4ea506fba000731778171\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\d00ad81142e4ea506fba000731778171\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\d00ad81142e4ea506fba000731778171\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\d00ad81142e4ea506fba000731778171\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\d00ad81142e4ea506fba000731778171\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\87c6bc5d9351f73b7b7de3739170458b\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\87c6bc5d9351f73b7b7de3739170458b\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\87c6bc5d9351f73b7b7de3739170458b\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\87c6bc5d9351f73b7b7de3739170458b\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\87c6bc5d9351f73b7b7de3739170458b\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\87c6bc5d9351f73b7b7de3739170458b\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\87c6bc5d9351f73b7b7de3739170458b\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:13:5-77
	android:name
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:13:22-74
activity#com.google.android.play.core.missingsplits.PlayCoreMissingSplitsActivity
ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:18:9-24:45
	android:process
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:23:13-64
	android:enabled
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:20:13-36
	android:stateNotNeeded
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:24:13-42
	android:launchMode
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:22:13-48
	android:exported
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:21:13-37
	android:name
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:19:13-100
activity#com.google.android.play.core.common.PlayCoreDialogWrapperActivity
ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:25:9-29:65
	android:stateNotNeeded
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:28:13-42
	android:exported
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:27:13-37
	android:theme
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:29:13-62
	android:name
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:26:13-93
service#com.google.android.play.core.assetpacks.AssetPackExtractionService
ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:32:9-39:19
	android:enabled
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:34:13-36
	android:exported
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:35:13-36
	android:name
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:33:13-94
meta-data#com.google.android.play.core.assetpacks.versionCode
ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:36:13-38:41
	android:value
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:38:17-38
	android:name
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:37:17-83
service#com.google.android.play.core.assetpacks.ExtractionForegroundService
ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:40:9-43:40
	android:enabled
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:42:13-36
	android:exported
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:43:13-37
	android:name
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\transforms-3\27314f1e78281c168520204ac38d180e\transformed\jetified-core-1.10.3\AndroidManifest.xml:41:13-95
