# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 44ms
    create-variant-model 14ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 11ms
    create-X86-model 31ms
    create-X86_64-model 18ms
    [gap of 30ms]
    create-module-model 81ms
    [gap of 12ms]
    create-ARMEABI_V7A-model 42ms
    create-ARM64_V8A-model 70ms
    create-X86-model 38ms
    create-X86_64-model 28ms
    create-module-model 13ms
    create-variant-model 20ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 12ms
    create-X86-model 12ms
    [gap of 11ms]
    create-X86_64-model 15ms
  create-initial-cxx-model completed in 542ms
create_cxx_tasks completed in 549ms

