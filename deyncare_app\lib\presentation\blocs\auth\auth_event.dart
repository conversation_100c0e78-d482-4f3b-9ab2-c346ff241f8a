import 'dart:io';
import 'package:equatable/equatable.dart';
import 'package:deyncare_app/domain/models/user.dart';
import 'package:deyncare_app/domain/models/auth_token.dart';

/// Base class for all authentication events
abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object?> get props => [];
}

/// Event triggered when the app starts
class AppStarted extends AuthEvent {}

/// Event triggered when user successfully logs in
class LoggedIn extends AuthEvent {
  final User user;
  final AuthToken token;

  const LoggedIn({
    required this.user,
    required this.token,
  });

  @override
  List<Object?> get props => [user, token];
}

/// Event triggered when user logs out
class LoggedOut extends AuthEvent {}

/// Event triggered when token is refreshed
class TokenRefreshed extends AuthEvent {
  final AuthToken token;

  const TokenRefreshed({required this.token});

  @override
  List<Object?> get props => [token];
}

/// Event triggered when session times out or is invalidated
class SessionExpired extends AuthEvent {}

/// Event triggered when user requests a password reset
class ForgotPasswordRequested extends AuthEvent {
  final String email;

  const ForgotPasswordRequested({required this.email});

  @override
  List<Object?> get props => [email];
}

/// Event triggered when user attempts to verify their email
class VerifyEmailRequested extends AuthEvent {
  final String email;
  final String verificationCode;
  final String? selectedPlanId;

  const VerifyEmailRequested({
    required this.email,
    required this.verificationCode,
    this.selectedPlanId,
  });

  @override
  List<Object?> get props => [email, verificationCode, selectedPlanId];
}

/// Event triggered when user requests to resend verification code
class ResendVerificationCodeRequested extends AuthEvent {
  final String email;

  const ResendVerificationCodeRequested({required this.email});

  @override
  List<Object?> get props => [email];
}

/// Event triggered when user wants to change their password
class ChangePasswordRequested extends AuthEvent {
  final String currentPassword;
  final String newPassword;
  final String confirmPassword;

  const ChangePasswordRequested({
    required this.currentPassword,
    required this.newPassword,
    required this.confirmPassword,
  });

  @override
  List<Object?> get props => [currentPassword, newPassword, confirmPassword];
}

/// Event triggered when password reset is successful via the hybrid approach
class ResetPasswordSuccess extends AuthEvent {
  /// No parameters needed as this is just a notification that the reset was successful
  const ResetPasswordSuccess();
}

/// Event triggered when initial registration is requested
class InitRegistrationRequested extends AuthEvent {
  final String fullName;
  final String email;
  final String phone;
  final String password;
  final String shopName;
  final String shopAddress;
  final String planType;
  final String paymentMethod;
  final bool initialPaid;
  final String? discountCode;

  const InitRegistrationRequested({
    required this.fullName,
    required this.email,
    required this.phone,
    required this.password,
    required this.shopName,
    required this.shopAddress,
    this.planType = 'trial',
    this.paymentMethod = 'offline',
    this.initialPaid = false,
    this.discountCode,
  });

  @override
  List<Object?> get props => [
        fullName,
        email,
        phone,
        password,
        shopName,
        shopAddress,
        planType,
        paymentMethod,
        initialPaid,
        discountCode,
      ];
}

/// Event triggered when payment processing is requested
class ProcessPaymentRequested extends AuthEvent {
  final String userId;
  final String shopId;
  final String planId;
  final String paymentMethod;
  final String phoneNumber;
  final double? amount;
  final String? discountCode;
  // Offline payment fields
  final String? payerName;
  final String? payerPhone;
  final String? notes;
  final File? paymentProof;

  const ProcessPaymentRequested({
    required this.userId,
    required this.shopId,
    required this.planId,
    required this.paymentMethod,
    required this.phoneNumber,
    this.amount,
    this.discountCode,
    // Offline payment fields
    this.payerName,
    this.payerPhone,
    this.notes,
    this.paymentProof,
  });

  @override
  List<Object?> get props => [
    userId,
    shopId,
    planId,
    paymentMethod,
    phoneNumber,
    amount,
    discountCode,
    payerName,
    payerPhone,
    notes,
    paymentProof,
  ];
}

/// Event triggered to fetch available payment methods from the backend.
class AvailablePaymentMethodsRequested extends AuthEvent {
  final String context;

  const AvailablePaymentMethodsRequested({required this.context});

  @override
  List<Object?> get props => [context];
}

/// Event triggered when user cancels payment processing
class CancelPaymentRequested extends AuthEvent {
  const CancelPaymentRequested();

  @override
  List<Object?> get props => [];
}

/// Event triggered when user wants to retry a failed operation
class RetryLastOperation extends AuthEvent {
  const RetryLastOperation();

  @override
  List<Object?> get props => [];
}

/// Event triggered to check if current user's account is suspended
class CheckAccountSuspensionRequested extends AuthEvent {
  final User user;

  const CheckAccountSuspensionRequested({required this.user});

  @override
  List<Object?> get props => [user];
}

/// Event triggered when account suspension is detected during login or API calls
class SuspensionDetected extends AuthEvent {
  final String reason;
  final DateTime? suspendedAt;
  final String? suspendedBy;

  const SuspensionDetected({
    required this.reason,
    this.suspendedAt,
    this.suspendedBy,
  });

  @override
  List<Object?> get props => [reason, suspendedAt, suspendedBy];
}

/// Event triggered to validate user role for mobile app access
class ValidateUserRoleRequested extends AuthEvent {
  final User user;

  const ValidateUserRoleRequested({required this.user});

  @override
  List<Object?> get props => [user];
}

/// Event triggered during periodic user status validation
class PeriodicUserStatusCheck extends AuthEvent {
  const PeriodicUserStatusCheck();

  @override
  List<Object?> get props => [];
}

/// Event triggered to update countdown timer for redirection
class UpdateRedirectCountdown extends AuthEvent {
  final int countdown;

  const UpdateRedirectCountdown({required this.countdown});

  @override
  List<Object?> get props => [countdown];
}

/// Event triggered when suspension popup is dismissed or timer completes
class CompleteSuspensionRedirect extends AuthEvent {
  const CompleteSuspensionRedirect();

  @override
  List<Object?> get props => [];
}
