const { Subscription, Plan } = require('../../models');
const { AppError, idGenerator, logSuccess, logError } = require('../../utils');

/**
 * Create a subscription record for a shop
 * @param {Object} subscriptionData - Subscription data
 * @param {Object} options - Additional options
 * @returns {Promise<Object>} Created subscription
 */
const createSubscription = async (subscriptionData, options = {}) => {
  try {
    const {
      shopId,
      planType = 'trial',
      planId = null,
      planName = null,
      pricing = {},
      paymentMethod = 'free',
      paymentDetails = null,
      discountDetails = null,
      session = null
    } = subscriptionData;

    if (!shopId) {
      throw new AppError('Shop ID is required to create subscription', 400, 'missing_shop_id');
    }

    // If planId is provided but no planName, try to get plan details
    let finalPlanName = planName;
    let finalPlanType = planType;

    if (planId && !planName) {
      try {
        const plan = await Plan.findOne({ planId: planId });
        if (plan) {
          finalPlanName = plan.displayName || plan.name;
          finalPlanType = plan.type || planType;
        }
      } catch (error) {
        logError(`Failed to fetch plan details for planId: ${planId}`, 'SubscriptionService');
      }
    }

    // Set default plan name if still not available
    if (!finalPlanName) {
      finalPlanName = finalPlanType === 'trial' ? 'Trial Plan' :
                     finalPlanType === 'monthly' ? 'Monthly Plan' :
                     finalPlanType === 'yearly' ? 'Yearly Plan' : 'Default Plan';
    }

    // Generate unique subscription ID using timestamp + random
    const subscriptionId = `SUB_${Date.now()}_${Math.random().toString(36).substr(2, 6).toUpperCase()}`;

    // Create subscription object with required plan fields
    const subscription = new Subscription({
      subscriptionId,
      shopId,
      planId,
      plan: {
        name: finalPlanName,
        type: finalPlanType
      },
      pricing,
      paymentMethod,
      paymentDetails,
      discountDetails,
      status: 'active',
      createdAt: new Date()
    });

    // Save with session if provided
    if (session) {
      await subscription.save({ session });
    } else {
      await subscription.save();
    }

    logSuccess(`Subscription created: ${subscriptionId} for shop: ${shopId}`, 'SubscriptionService');
    return subscription;

  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    
    logError(`Error creating subscription: ${error.message}`, 'SubscriptionService', error);
    throw new AppError('Failed to create subscription', 500, 'subscription_creation_error');
  }
};

module.exports = createSubscription; 
