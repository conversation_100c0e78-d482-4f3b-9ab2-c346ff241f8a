require('./scheduler');
const express = require('express');
const dotenv = require('dotenv');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const cookieParser = require('cookie-parser');
const compression = require('compression');
const session = require('express-session');
const crypto = require('crypto');
const { logInfo, logError, logDatabase, logSuccess, AppError, ErrorResponse } = require('./utils');
const { applyRateLimiters } = require('./middleware/rateLimiters');
const csrfMiddleware = require('./middleware/csrfMiddleware');
const { generateCsrfToken, validateCsrfToken, clearCsrfToken, requireCsrf } = csrfMiddleware;
const { validatePasswordMiddleware } = require('./middleware/passwordValidator');
const { sessionTimeoutMiddleware } = require('./middleware/sessionTimeout');
const { checkLoginLockoutMiddleware } = require('./middleware/loginProtection');
const SchedulerService = require('./services/schedulerService');

const authRoutes = require('./routes/authRoutes');
const subscriptionRoutes = require('./routes/subscriptionRoutes');
const settingsRoutes = require('./routes/settingsRoutes');
const discountRoutes = require('./routes/discountRoutes');
const userRoutes = require('./routes/userRoutes');
const reportRoutes = require('./routes/reportRoutes');
const shopRoutes = require('./routes/shopRoutes');
const planRoutes = require('./routes/planRoutes');
const registerRoutes = require('./routes/registerRoutes');
const publicRoutes = require('./routes/publicRoutes');
const paymentRoutes = require('./routes/paymentRoutes');
const registrationPaymentRoutes = require('./routes/registrationPaymentRoutes');
const appUploadRoutes = require('./routes/appUploadRoutes');
// Export Routes
const exportRoutes = require('./routes/exportRoutes');
// Subscription Status Routes
const subscriptionStatusRoutes = require('./routes/subscriptionStatusRoutes');
// Simplified SuperAdmin Routes
const superAdminRoutes = require('./routes/superAdminRoutes');
// ML Integration Routes
const debtRoutes = require('./routes/debtRoutes');
const customerRoutes = require('./routes/customerRoutes');
const riskRoutes = require('./routes/riskRoutes');
// Push Notification Routes
const pushNotificationRoutes = require('./routes/pushNotificationRoutes');
const fcmRoutes = require('./routes/fcmRoutes');
// Import other routes as they are created

dotenv.config();

const app = express();

/**
 * Security configuration
 * Enhanced security with CSP headers, CSRF protection, and secure cookies
 */

// Function to generate a CSP nonce for each request
app.use((req, res, next) => {
  try {
    // Generate random nonce for CSP headers
    res.locals.cspNonce = crypto.randomBytes(16).toString('base64');
  } catch (error) {
    // Fallback if crypto fails for any reason
    res.locals.cspNonce = Math.random().toString(36).substring(2, 15);
    console.log('Using fallback nonce generation');
  }
  next();
});

// Enhanced Helmet configuration with CSP headers
app.use(helmet({
  contentSecurityPolicy: {
    useDefaults: true,
    directives: {
      defaultSrc: ["'self'"],
      // Use nonce-based CSP for scripts instead of unsafe-inline
      scriptSrc: [
        "'self'", 
        (req, res) => `'nonce-${res.locals.cspNonce}'`,
        // Only use unsafe-eval in development
        ...(process.env.NODE_ENV === 'development' ? ["'unsafe-eval'"] : [])
      ],
      connectSrc: [
        "'self'", 
        process.env.FRONTEND_URL || '*',
        // Add API endpoints if needed
        ...(process.env.API_ENDPOINTS ? process.env.API_ENDPOINTS.split(',') : [])
      ],
      imgSrc: ["'self'", 'data:', 'blob:'],
      styleSrc: ["'self'", "'unsafe-inline'"], // Styles often need inline
      fontSrc: ["'self'", 'data:'],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
      // Add frame-ancestors to prevent clickjacking
      frameAncestors: ["'none'"],
      // Add form-action for forms
      formAction: ["'self'"],
      // Add upgrade-insecure-requests
      upgradeInsecureRequests: process.env.NODE_ENV === 'production' ? [] : null,
      // Add report-uri for CSP violations reporting
      'report-uri': process.env.CSP_REPORT_URI ? [process.env.CSP_REPORT_URI] : null
    },
    reportOnly: process.env.CSP_REPORT_ONLY === 'true'
  },
  // Other security headers
  xssFilter: true,
  noSniff: true,
  referrerPolicy: { policy: 'strict-origin-when-cross-origin' },
  // Add HSTS in production
  hsts: process.env.NODE_ENV === 'production' ? {
    maxAge: 31536000, // 1 year
    includeSubDomains: true,
    preload: true
  } : false
}));

// Configure CORS
const corsOptions = {
  origin: (origin, callback) => {
    // Allow requests with no origin (like mobile apps, curl, etc)
    if (!origin) return callback(null, true);
    
    // Check against allowed origins - trim whitespace from each origin
    const allowedOrigins = (process.env.CORS_ORIGIN || '*').split(',').map(origin => origin.trim());
    
    // Log for debugging in production
    console.log('CORS check - Origin:', origin, 'Allowed:', allowedOrigins);
    
    if (allowedOrigins.indexOf('*') !== -1 || allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-CSRF-Token', 'X-Requested-With', 'Cache-Control', 'Pragma', 'Expires'],
  preflightContinue: false,
  optionsSuccessStatus: 200
};
app.use(cors(corsOptions));
app.use(express.json({ limit: '10kb' }));
app.use(express.urlencoded({ extended: true, limit: '10kb' }));

// Configure secure cookies with enhanced security
app.use(cookieParser(process.env.COOKIE_SECRET || 'deyncare-secret-key'));

// Centralized cookie security configuration
const secureCookieConfig = {
  // Always use secure cookies if SECURE_COOKIES is true, otherwise only in production
  secure: process.env.SECURE_COOKIES === 'true' || process.env.NODE_ENV === 'production',
  httpOnly: true, // Prevent JavaScript access
  sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax', // Allow cross-origin in production, lax for development
  maxAge: parseInt(process.env.COOKIE_MAX_AGE) || 24 * 60 * 60 * 1000, // Default 24 hours
  domain: process.env.COOKIE_DOMAIN || undefined,
  path: '/',
  signed: true
};

// Session configuration with enhanced security
const sessionConfig = {
  secret: process.env.SESSION_SECRET || 'deyncare-session-secret',
  name: 'deyncare.sid', // Custom name instead of default connect.sid
  resave: false,
  saveUninitialized: false,
  cookie: secureCookieConfig
};

// Try to add Redis store for session in production if configured
if (process.env.REDIS_URL && process.env.NODE_ENV === 'production') {
  try {
    // Try to load Redis modules
    const RedisStore = require('connect-redis').default;
    const { createClient } = require('redis');
    
    // Create Redis client and store
    const redisClient = createClient({ url: process.env.REDIS_URL });
    redisClient.connect().catch(console.error);
    sessionConfig.store = new RedisStore({ client: redisClient });
    
    console.log('Redis session store initialized');
  } catch (error) {
    console.log('Redis session store not available, using memory store:', error.message);
    // Continue without Redis - will use default memory store
  }
}

app.use(session(sessionConfig));

// Helper middleware to set secure cookie defaults
app.use((req, res, next) => {
  // Override res.cookie to always use secure defaults
  const originalCookie = res.cookie;
  res.cookie = function(name, value, options = {}) {
    const secureOptions = { ...secureCookieConfig, ...options };
    return originalCookie.call(this, name, value, secureOptions);
  };
  next();
});

app.use(compression());

if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
}

// File serving routes - handle both fileId-based and direct path access
const fileController = require('./controllers/fileController');

// Serve files by fileId (primary method)
app.get('/api/files/:fileId', fileController.serveFile);

// Get file metadata
app.get('/api/files/:fileId/metadata', fileController.getFileMetadata);

// Test endpoint to list uploaded shop logos (development only)
if (process.env.NODE_ENV === 'development') {
  app.get('/api/files/test/shop-logos', async (req, res) => {
    try {
      const { File } = require('./models');
      const logos = await File.find({
        fileType: 'logo',
        isDeleted: false
      }).select('fileId fileName originalName shopId url uploadedAt').limit(10);

      res.json({
        success: true,
        message: 'Shop logos found',
        count: logos.length,
        data: logos
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Error fetching shop logos',
        error: error.message
      });
    }
  });
}

// Fallback: serve files directly from uploads directory (for backward compatibility)
const path = require('path');
const fs = require('fs');

app.use('/api/files/direct', express.static(path.join(__dirname, '../uploads'), {
  dotfiles: 'deny',
  index: false,
  maxAge: '1d',
  setHeaders: (res, path) => {
    // Set security headers for direct file access
    res.set('X-Content-Type-Options', 'nosniff');
    if (path.match(/\.(jpg|jpeg|png|gif|webp)$/i)) {
      res.set('Content-Disposition', 'inline');
    }
  }
}));

// Apply rate limiters
applyRateLimiters(app);

// Configure CSRF protection

// Skip CSRF for login/logout but generate token after successful login
app.use('/api/auth/login', (req, res, next) => {
  // This route doesn't need CSRF validation but will generate a token after successful login
  if (req.method === 'POST') {
    // Skip CSRF validation for login
    next();
  } else {
    validateCsrfToken(req, res, next);
  }
});

// Clear CSRF token on logout
app.use('/api/auth/logout', (req, res, next) => {
  if (req.method === 'POST') {
    // Let the request proceed and clear token after
    const originalEnd = res.end;
    res.end = function(...args) {
      clearCsrfToken(req, res, () => {});
      return originalEnd.apply(this, args);
    };
  }
  next();
});

// Make CSRF functions available to controllers and apply token generation
app.use((req, res, next) => {
  // Store CSRF middleware functions in res.locals for access in controllers
  res.locals.csrfMiddleware = {
    generateToken: generateCsrfToken,
    validateToken: validateCsrfToken,
    clearToken: clearCsrfToken,
    requireCsrf: requireCsrf,
    generateAndSetToken: csrfMiddleware.generateAndSetToken // Add the non-middleware function
  };
  next();
});

// Apply password validation to all routes that create or update passwords
app.use('/api/auth/register', validatePasswordMiddleware);
app.use('/api/auth/reset-password', validatePasswordMiddleware);
app.use('/api/auth/change-password', validatePasswordMiddleware);

// Apply session timeout to all authenticated routes
// This must be applied after session middleware is set up
app.use(sessionTimeoutMiddleware);

// Apply login protection to check for account lockouts
app.use(checkLoginLockoutMiddleware);

// Generate CSRF token for all API routes
app.use('/api', generateCsrfToken);

// Apply CSRF validation to all mutating API operations except login
app.use('/api', (req, res, next) => {
  // Skip validation for safe methods
  if (['GET', 'HEAD', 'OPTIONS'].includes(req.method)) {
    return next();
  }
  
  // Skip for specific routes that handle their own CSRF
  if (req.path.startsWith('/auth/login')) {
    return next();
  }
  
  // Apply CSRF protection to all other routes
  validateCsrfToken(req, res, next);
});

// Explicitly protect sensitive routes with CSRF
app.use('/api/auth/change-password', requireCsrf);
app.use('/api/auth/reset-password', requireCsrf);
app.use('/api/users', requireCsrf);
app.use('/api/shops', requireCsrf);
app.use('/api/plans', requireCsrf);

// Public routes for clients (no auth required)
app.use('/api/public', publicRoutes);

// Authentication routes (login, logout, password management)
// NOTE: User registration moved to /api/register/* endpoints
app.use('/api/auth', authRoutes);
app.use('/api/auth/payment-retry', require('./routes/paymentRetryRoutes'));
app.use('/api/subscriptions', subscriptionRoutes);
app.use('/api/settings', settingsRoutes);
app.use('/api/discounts', discountRoutes);
app.use('/api/users', userRoutes);
app.use('/api/reports', reportRoutes);
app.use('/api/shops', shopRoutes);
app.use('/api/plans', planRoutes);
// App upload routes for application file management
app.use('/api/app', appUploadRoutes);
// Export routes for data export functionality
app.use('/api/export', exportRoutes);
// Subscription status routes (for mobile apps)
app.use('/api/subscription-status', subscriptionStatusRoutes);
// Subscription renewal routes (integrated with existing payment system)
const subscriptionRenewalRoutes = require('./routes/subscriptionRenewalRoutes');
app.use('/api/subscriptions', subscriptionRenewalRoutes);
// New modular registration system (includes SuperAdmin operations)
app.use('/api/register', registerRoutes);
app.use('/api/payments', paymentRoutes);
app.use('/api/registration-payment', registrationPaymentRoutes);
// Simplified SuperAdmin CRUD Operations
app.use('/api/admin', superAdminRoutes);
// ML Integration Routes
app.use('/api/debts', debtRoutes);
app.use('/api/customers', customerRoutes);
app.use('/api/risk', riskRoutes);
// Push Notification Routes
app.use('/api/admin/notifications/push', pushNotificationRoutes);
app.use('/api/fcm', fcmRoutes);

// Routes registered successfully
// Add other routes as they are created

app.get('/api/health', (req, res) => {
  res.status(200).json({
    status: 'success',
    message: 'DeynCare API is running',
    environment: process.env.NODE_ENV || 'development',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

app.get('/', (req, res) => {
  res.status(200).json({
    name: 'DeynCare API',
    version: '1.1.0',
    description: 'Debt management system API',
    endpoints: {
      auth: '/api/auth',
      health: '/api/health',
      settings: '/api/settings',
      shops: '/api/shops',
      plans: '/api/plans',
      subscriptions: '/api/subscriptions',
      payments: '/api/payments',
      registrationPayment: '/api/registration-payment',
      export: '/api/export',
      subscriptionStatus: '/api/subscription-status',
      debts: '/api/debts',
      customers: '/api/customers',
      risk: '/api/risk',
      superAdmin: '/api/admin'
    }
  });
});

app.use('*', (req, res, next) => {
  next(new AppError(`Cannot find ${req.originalUrl} on this server`, 404, 'not_found'));
});

app.use((err, req, res, next) => {
  logError(err.message, 'Global Error Handler', {
    url: req.originalUrl,
    method: req.method,
    statusCode: err.statusCode || 500,
    stack: process.env.NODE_ENV === 'development' ? err.stack : undefined
  });

  const errorResponse = ErrorResponse.fromError(err);
  res.status(errorResponse.statusCode || 500).json(errorResponse);
});

process.on('unhandledRejection', (err) => {
  logError(`Unhandled Rejection: ${err.message}`, 'Process', err);
  
  if (process.env.NODE_ENV === 'production') {
    console.log('Shutting down gracefully...');
    process.exit(1);
  }
});

process.on('uncaughtException', (err) => {
  logError(`Uncaught Exception: ${err.message}`, 'Process', err);
  
  console.log('Shutting down immediately due to uncaught exception');
  process.exit(1);
});

module.exports = app;