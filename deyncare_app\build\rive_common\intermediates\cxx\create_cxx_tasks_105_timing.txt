# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 14ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 16ms
    create-X86_64-model 13ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 24ms
    create-ARM64_V8A-model 11ms
    create-X86-model 12ms
    create-X86_64-model 10ms
    [gap of 48ms]
  create-initial-cxx-model completed in 206ms
create_cxx_tasks completed in 212ms

