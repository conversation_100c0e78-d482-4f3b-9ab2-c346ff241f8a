import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/core/utils/toast_util.dart';
import 'package:deyncare_app/presentation/screens/auth/widgets/auth_button.dart';
import 'package:deyncare_app/presentation/screens/auth/widgets/auth_text_field.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_bloc.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_event.dart';
import 'package:deyncare_app/presentation/blocs/auth/auth_state.dart';
import 'package:deyncare_app/data/services/discount_service.dart';
import 'package:deyncare_app/presentation/screens/auth/payment/widgets/offline_payment_form.dart';

/// Enhanced payment form component with modern UI following registration layout pattern
class PaymentForm extends StatefulWidget {
  /// The name of the selected plan
  final String planName;
  
  /// The amount to be paid
  final double planAmount;
  
  /// Initial phone number if available
  final String? initialPhoneNumber;

  /// Indicates if payment is currently being processed
  final bool isProcessingPayment;

  /// The type of the selected plan
  final String planType;
  
  /// Error message to display if payment failed
  final String? errorMessage;

  const PaymentForm({
    super.key,
    required this.planName,
    required this.planAmount,
    this.initialPhoneNumber,
    this.isProcessingPayment = false,
    required this.planType,
    this.errorMessage,
  });

  @override
  State<PaymentForm> createState() => _PaymentFormState();
}

class _PaymentFormState extends State<PaymentForm> with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _phoneController;
  late TextEditingController _discountController;
  String? _selectedPaymentMethod;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  // Retry functionality
  int _retryCount = 0;
  bool _isRetrying = false;
  
  // Discount state
  bool _isValidatingDiscount = false;
  bool _discountApplied = false;
  Map<String, dynamic>? _discountDetails;
  String? _discountError;

  // Offline payment state
  Map<String, dynamic>? _offlinePaymentData;

  // Payment method mapping - Normalize backend response to internal format
  static const Map<String, String> _paymentMethodMapping = {
    // Backend format -> Internal format
    'EVC Plus': 'evc_plus',
    'evc_plus': 'evc_plus',
    'EVC_PLUS': 'evc_plus',
    'offline': 'offline',
    'Offline Payment': 'offline',
    'OFFLINE': 'offline',
    'zaad': 'zaad',
    'Zaad Service': 'zaad',
    'ZAAD': 'zaad',
    'sahal': 'sahal',
    'Sahal': 'sahal',
    'SAHAL': 'sahal',
  };

  // Reverse mapping - Internal format -> Backend format
  static const Map<String, String> _backendPaymentMethodMapping = {
    'evc_plus': 'EVC Plus',
    'offline': 'offline',
    'zaad': 'zaad',
    'sahal': 'sahal',
  };

  @override
  void initState() {
    super.initState();
    _phoneController = TextEditingController(text: widget.initialPhoneNumber ?? '');
    _discountController = TextEditingController();

    // Initialize animations
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    // Start animation
    _animationController.forward();

    // Fetch payment methods
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        final currentState = context.read<AuthBloc>().state;
        if (currentState is AuthPaymentRequired) {
          context
              .read<AuthBloc>()
              .add(const AvailablePaymentMethodsRequested(context: 'subscription'));
        }
      }
    });
  }

  @override
  void dispose() {
    _phoneController.dispose();
    _discountController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  /// Normalize payment method from backend response to internal format
  String _normalizePaymentMethod(String backendMethod) {
    return _paymentMethodMapping[backendMethod] ?? backendMethod.toLowerCase().replaceAll(' ', '_');
  }

  /// Get backend format for payment method
  String _getBackendPaymentMethod(String internalMethod) {
    return _backendPaymentMethodMapping[internalMethod] ?? internalMethod;
  }

  String? _validatePhone(String? value) {
    if (_selectedPaymentMethod == 'evc_plus') {
      if (value != null && value.isNotEmpty) {
        final phoneRegex = RegExp(r'^\+?[\d\s-]{8,}$');
        if (!phoneRegex.hasMatch(value)) {
          return 'Please enter a valid phone number';
        }
      }
    }
    return null;
  }

  // Validate discount code
  Future<void> _validateDiscountCode() async {
    if (_discountController.text.trim().isEmpty) {
      setState(() {
        _discountApplied = false;
        _discountDetails = null;
        _discountError = null;
      });
      return;
    }

    setState(() {
      _isValidatingDiscount = true;
      _discountError = null;
    });

    try {
      // Get auth token from current state
      final authState = context.read<AuthBloc>().state;
      if (authState is! AuthPaymentRequired) {
        throw Exception('Invalid authentication state');
      }

      // Check if we have a valid access token
      final accessToken = authState.user.accessToken;
      if (accessToken == null || accessToken.isEmpty) {
        // During registration, user might not have a valid token yet
        // For now, we'll do basic client-side validation and defer server validation
        setState(() {
          _discountApplied = true;
          _discountDetails = {
            'code': _discountController.text.trim().toUpperCase(),
            'type': 'percentage', // Default assumption
            'value': 10.0, // Default 10% discount assumption
            'discountAmount': widget.planAmount * 0.1, // 10% discount
            'finalAmount': widget.planAmount * 0.9,
            'description': 'Discount code will be validated during payment processing',
            'discountId': 'pending_validation',
          };
          _discountError = null;
        });
        ToastUtil.showInfo('Discount code added. It will be validated during payment processing.');
        return;
      }

      // Use real discount service if we have a valid token
      final result = await DiscountService.validateDiscountCode(
        code: _discountController.text.trim(),
        amount: widget.planAmount,
        context: 'subscription',
        authToken: accessToken,
      );

      if (result.isValid && result.discountCode != null) {
        final discountCode = result.discountCode!;
        final discountAmount = discountCode.calculateDiscount(widget.planAmount);
        
        setState(() {
          _discountApplied = true;
          _discountDetails = {
            'code': discountCode.code,
            'type': discountCode.type,
            'value': discountCode.value,
            'discountAmount': discountAmount,
            'finalAmount': widget.planAmount - discountAmount,
            'description': discountCode.description,
            'discountId': discountCode.discountId,
          };
          _discountError = null;
        });
        ToastUtil.showSuccess(result.message);
      } else {
        setState(() {
          _discountApplied = false;
          _discountDetails = null;
          _discountError = result.message;
        });
      }
    } catch (error) {
      // If validation fails, allow the discount code to be submitted for server-side validation
      if (error.toString().contains('401') || error.toString().contains('User not found')) {
        setState(() {
          _discountApplied = true;
          _discountDetails = {
            'code': _discountController.text.trim().toUpperCase(),
            'type': 'percentage',
            'value': 0.0, // Unknown value, will be determined by server
            'discountAmount': 0.0, // Will be calculated by server
            'finalAmount': widget.planAmount,
            'description': 'Discount code will be validated during payment processing',
            'discountId': 'pending_validation',
          };
          _discountError = null;
        });
        ToastUtil.showInfo('Discount code added. It will be validated during payment processing.');
      } else {
        setState(() {
          _discountApplied = false;
          _discountDetails = null;
          _discountError = error.toString().contains('ApiException') 
              ? error.toString().split(': ').last
              : 'Failed to validate discount code';
        });
      }
    } finally {
      setState(() {
        _isValidatingDiscount = false;
      });
    }
  }

  // Remove discount
  void _removeDiscount() {
    setState(() {
      _discountController.clear();
      _discountApplied = false;
      _discountDetails = null;
      _discountError = null;
    });
    ToastUtil.showInfo('Discount code removed');
  }

  // Calculate final amount with discount
  double _getFinalAmount() {
    if (_discountApplied && _discountDetails != null) {
      return _discountDetails!['finalAmount']?.toDouble() ?? widget.planAmount;
    }
    return widget.planAmount;
  }

  void _handlePayment() {
    if (!(_formKey.currentState?.validate() ?? false)) return;

    if (_selectedPaymentMethod == null) {
      ToastUtil.showError('Please select a payment method.');
      return;
    }

    final authState = context.read<AuthBloc>().state;
    if (authState is! AuthPaymentRequired) {
      ToastUtil.showError('Invalid state, please restart the process.');
      return;
    }

    final user = authState.user;
    if (user.shopId == null) {
      ToastUtil.showError('Shop information is missing.');
      return;
    }

    // Increment retry count for analytics
    setState(() {
      _retryCount++;
      _isRetrying = false;
    });

    // Get the backend format for the payment method
    final backendPaymentMethod = _getBackendPaymentMethod(_selectedPaymentMethod!);
    
    // DEBUG: Log the exact payment data being sent
    debugPrint('🔍 FLUTTER PAYMENT DEBUG:');
    debugPrint('  Selected Payment Method (Internal): $_selectedPaymentMethod');
    debugPrint('  Backend Payment Method: $backendPaymentMethod');
    debugPrint('  User ID: ${user.userId}');
    debugPrint('  Shop ID: ${user.shopId}');
    debugPrint('  Plan Type: ${widget.planType}');
    debugPrint('  Phone Number: ${_phoneController.text.trim()}');
    debugPrint('  Amount: ${_getFinalAmount()}');
    debugPrint('  Discount Code: ${_discountController.text.trim()}');
    debugPrint('  User Access Token: ${user.accessToken?.substring(0, 20)}...');

    context.read<AuthBloc>().add(ProcessPaymentRequested(
          userId: user.userId,
          shopId: user.shopId!,
          planId: widget.planType,
          paymentMethod: backendPaymentMethod, // Use backend format
          phoneNumber: _phoneController.text.trim(),
          amount: _getFinalAmount(),
          discountCode: _discountController.text.trim(),
          // Offline payment fields
          payerName: _offlinePaymentData?['payerName'],
          payerPhone: _offlinePaymentData?['payerPhone'],
          notes: _offlinePaymentData?['notes'],
          paymentProof: _offlinePaymentData?['paymentProof'],
        ));
  }

  void _handleRetryPayment() {
    setState(() {
      _isRetrying = true;
    });
    
    // Add a small delay for better UX
    Future.delayed(const Duration(milliseconds: 500), () {
      _handlePayment();
    });
  }

  String _formatPaymentMethodName(String method) {
    switch (method) {
      case 'evc_plus':
        return 'EVC Plus';
      case 'offline':
        return 'Offline Payment';
      case 'zaad':
        return 'Zaad Service';
      case 'sahal':
        return 'Sahal';
      default:
        return method
            .replaceAll('_', ' ')
            .split(' ')
            .map((str) => str.isNotEmpty ? str[0].toUpperCase() + str.substring(1) : '')
            .join(' ');
    }
  }

  IconData _getPaymentMethodIcon(String method) {
    switch (method) {
      case 'evc_plus':
        return Icons.phone_android;
      case 'offline':
        return Icons.store;
      case 'zaad':
        return Icons.account_balance_wallet;
      case 'sahal':
        return Icons.payment;
      default:
        return Icons.payment;
    }
  }

  String _getPaymentMethodDescription(String method) {
    switch (method) {
      case 'evc_plus':
        return 'Pay using EVC Plus mobile money';
      case 'offline':
        return 'Visit our office or pay with cash';
      case 'zaad':
        return 'Pay using Zaad mobile money';
      case 'sahal':
        return 'Pay using Sahal service';
      default:
        return 'Secure payment method';
    }
  }

  Widget _buildPaymentMethodCard(String backendMethod) {
    // Normalize the payment method from backend
    final method = _normalizePaymentMethod(backendMethod);
    final bool isSelected = _selectedPaymentMethod == method;
    
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedPaymentMethod = method; // Store normalized format
          });
        },
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: isSelected
                ? AppThemes.primaryColor.withValues(alpha: 0.1)
                : AppThemes.textFieldBackgroundColor,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: isSelected ? AppThemes.primaryColor : AppThemes.dividerColor,
              width: isSelected ? 2 : 1,
            ),
            boxShadow: isSelected
                ? [
                    BoxShadow(
                      color: AppThemes.primaryColor.withValues(alpha: 0.2),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    )
                  ]
                : [],
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: isSelected
                      ? AppThemes.primaryColor
                      : AppThemes.textSecondaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  _getPaymentMethodIcon(method),
                  color: isSelected
                      ? Theme.of(context).colorScheme.onPrimary
                      : AppThemes.textSecondaryColor,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _formatPaymentMethodName(method),
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _getPaymentMethodDescription(method),
                      style: TextStyle(
                        fontSize: 14,
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
              if (isSelected)
                Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: AppThemes.primaryColor,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.check,
                    color: Theme.of(context).colorScheme.onPrimary,
                    size: 16,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPlanSummaryCard() {
    final originalAmount = widget.planAmount;
    final finalAmount = _getFinalAmount();
    final hasDiscount = _discountApplied && _discountDetails != null;
    
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppThemes.primaryColor.withValues(alpha: 0.1),
            AppThemes.primaryColor.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppThemes.primaryColor.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppThemes.primaryColor,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.workspace_premium,
                  color: Theme.of(context).colorScheme.onPrimary,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Selected Plan',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppThemes.textSecondaryColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      widget.planName,
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppThemes.dividerColor,
                width: 1,
              ),
            ),
            child: Column(
              children: [
                // Original amount (if discount applied)
                if (hasDiscount) ...[
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Original Amount',
                        style: TextStyle(
                          fontSize: 14,
                          color: AppThemes.textSecondaryColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        '\$${originalAmount.toStringAsFixed(2)}',
                        style: TextStyle(
                          fontSize: 16,
                          color: AppThemes.textSecondaryColor,
                          decoration: TextDecoration.lineThrough,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  // Discount amount
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.discount,
                            size: 16,
                            color: Colors.green,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'Discount (${_discountDetails!['code']})',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.green,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                      Text(
                        '-\$${_discountDetails!['discountAmount'].toStringAsFixed(2)}',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.green,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Divider(color: AppThemes.dividerColor),
                  const SizedBox(height: 8),
                ],
                // Final amount
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      hasDiscount ? 'Final Amount' : 'Total Amount',
                      style: TextStyle(
                        fontSize: 16,
                        color: AppThemes.textSecondaryColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      '\$${finalAmount.toStringAsFixed(2)}',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: hasDiscount ? AppThemes.successColor : AppThemes.primaryColor,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Text(
                'Complete Payment',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: AppThemes.textPrimaryColor,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Your account is verified. Complete payment to activate your subscription.',
                style: TextStyle(
                  fontSize: 16,
                  color: AppThemes.textSecondaryColor,
                ),
              ),
              const SizedBox(height: 32),

              // Error Message Display
              if (widget.errorMessage != null) ...[
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: AppThemes.errorColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: AppThemes.errorColor.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.error_outline,
                            color: AppThemes.errorColor,
                            size: 24,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              'Payment Failed',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: AppThemes.errorColor,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Text(
                        widget.errorMessage!,
                        style: TextStyle(
                          fontSize: 14,
                          color: AppThemes.errorColor,
                          height: 1.4,
                        ),
                      ),
                      if (widget.errorMessage!.contains('declined by the payment provider')) ...[
                        const SizedBox(height: 16),
                        Row(
                          children: [
                            Icon(
                              Icons.lightbulb_outline,
                              color: AppThemes.warningColor,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                'Try selecting "Offline Payment" below for manual processing.',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: AppThemes.warningColor,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: _isRetrying ? null : _handleRetryPayment,
                              icon: _isRetrying 
                                  ? SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor: AlwaysStoppedAnimation<Color>(Theme.of(context).colorScheme.onPrimary),
                                      ),
                                    )
                                  : Icon(Icons.refresh),
                              label: Text(_isRetrying ? 'Retrying...' : 'Retry Payment'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppThemes.primaryColor,
                                foregroundColor: Theme.of(context).colorScheme.onPrimary,
                                padding: const EdgeInsets.symmetric(vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          if (_retryCount > 0)
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                              decoration: BoxDecoration(
                                color: AppThemes.dividerColor.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                'Attempt $_retryCount',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: AppThemes.dividerColor,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24),
              ],

              // Plan Summary Card
              _buildPlanSummaryCard(),
              const SizedBox(height: 32),

              // Payment Methods Section
              BlocBuilder<AuthBloc, AuthState>(
                builder: (context, state) {
                  if (state is! AuthPaymentRequired) {
                    return Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: AppThemes.errorColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: AppThemes.errorColor.withValues(alpha: 0.3)),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.error_outline, color: AppThemes.errorColor),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              'Invalid state. Please restart the process.',
                              style: TextStyle(color: AppThemes.errorColor),
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  if (state.isLoadingPaymentMethods) {
                    return Container(
                      padding: const EdgeInsets.all(40),
                      child: Column(
                        children: [
                          CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(AppThemes.primaryColor),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Loading payment methods...',
                            style: TextStyle(
                              color: AppThemes.textSecondaryColor,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  if (state.errorMessage != null && state.availablePaymentMethods == null) {
                    return Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: AppThemes.warningColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: AppThemes.warningColor.withValues(alpha: 0.3)),
                      ),
                      child: Column(
                        children: [
                          Row(
                            children: [
                              Icon(Icons.warning_outlined, color: AppThemes.warningColor),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Text(
                                  state.errorMessage!,
                                  style: TextStyle(color: AppThemes.warningColor),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton.icon(
                              onPressed: () {
                                final currentState = context.read<AuthBloc>().state;
                                if (currentState is AuthPaymentRequired) {
                                  context.read<AuthBloc>().add(
                                      const AvailablePaymentMethodsRequested(
                                          context: 'subscription'));
                                }
                              },
                              icon: const Icon(Icons.refresh),
                              label: const Text('Retry'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppThemes.warningColor,
                                foregroundColor: Theme.of(context).colorScheme.onPrimary,
                                padding: const EdgeInsets.symmetric(vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  if (state.availablePaymentMethods == null ||
                      state.availablePaymentMethods!.isEmpty) {
                    return Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: AppThemes.dividerColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: AppThemes.dividerColor.withValues(alpha: 0.3)),
                      ),
                      child: Column(
                        children: [
                          Icon(Icons.payment_outlined, 
                               color: AppThemes.dividerColor, size: 48),
                          const SizedBox(height: 16),
                          Text(
                            'No payment methods available',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: AppThemes.dividerColor,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Please contact support for assistance.',
                            style: TextStyle(
                              color: AppThemes.dividerColor,
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Select Payment Method',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppThemes.textPrimaryColor,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Choose your preferred payment method to complete the transaction.',
                        style: TextStyle(
                          fontSize: 14,
                          color: AppThemes.textSecondaryColor,
                        ),
                      ),
                      const SizedBox(height: 20),
                      
                      // Payment Method Cards - Use backend method names with normalization
                      ...state.availablePaymentMethods!.map((backendMethod) => 
                          _buildPaymentMethodCard(backendMethod)),
                      
                      const SizedBox(height: 24),

                      // Phone Number Field (for mobile money)
                      if (_selectedPaymentMethod == 'evc_plus' ||
                          _selectedPaymentMethod == 'zaad')
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Phone Number',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: AppThemes.textPrimaryColor,
                              ),
                            ),
                            const SizedBox(height: 8),
                            AuthTextField(
                              label: 'Phone Number (Optional)',
                              hintText: 'Leave empty to use registered number',
                              controller: _phoneController,
                              keyboardType: TextInputType.phone,
                              prefixIcon: const Icon(Icons.phone_outlined),
                              validator: _validatePhone,
                            ),
                            const SizedBox(height: 24),
                          ],
                        ),

                      // Offline Payment Form (for offline payments)
                      if (_selectedPaymentMethod == 'offline')
                        Column(
                          children: [
                            BlocBuilder<AuthBloc, AuthState>(
                              builder: (context, state) {
                                if (state is AuthPaymentRequired) {
                                  final user = state.user;
                                  return OfflinePaymentForm(
                                    planName: widget.planName,
                                    planAmount: widget.planAmount,
                                    planType: widget.planType,
                                    initialPayerName: user.fullName,
                                    initialPayerPhone: user.phone,
                                    isLoading: widget.isProcessingPayment,
                                    onSubmit: (data) {
                                      setState(() {
                                        _offlinePaymentData = data;
                                      });
                                      // Auto-submit the payment after offline data is collected
                                      _handlePayment();
                                    },
                                  );
                                }
                                return const SizedBox.shrink();
                              },
                            ),
                            const SizedBox(height: 24),
                          ],
                        ),

                      // Discount Code Section (Available for all users)
                      Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              AppThemes.successColor.withValues(alpha: 0.1),
                              AppThemes.successColor.withValues(alpha: 0.05),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: AppThemes.successColor.withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.local_offer_outlined,
                                  color: AppThemes.successColor,
                                  size: 24,
                                ),
                                const SizedBox(width: 12),
                                Text(
                                  'Discount Code (Optional)',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: AppThemes.successColor,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            Row(
                              children: [
                                Expanded(
                                  child: AuthTextField(
                                    label: 'Discount Code',
                                    controller: _discountController,
                                    hintText: 'Enter discount code',
                                    keyboardType: TextInputType.text,
                                    prefixIcon: const Icon(Icons.confirmation_number_outlined),
                                    suffixIcon: _discountApplied
                                        ? IconButton(
                                            icon: const Icon(Icons.close, color: AppThemes.errorColor),
                                            onPressed: _removeDiscount,
                                          )
                                        : null,
                                  ),
                                ),
                                const SizedBox(width: 12),
                                ElevatedButton(
                                  onPressed: _isValidatingDiscount ? null : _validateDiscountCode,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: AppThemes.successColor,
                                    foregroundColor: Theme.of(context).colorScheme.onPrimary,
                                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                  ),
                                  child: _isValidatingDiscount
                                      ? SizedBox(
                                          width: 16,
                                          height: 16,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                            valueColor: AlwaysStoppedAnimation<Color>(Theme.of(context).colorScheme.onPrimary),
                                          ),
                                        )
                                      : const Text('Apply'),
                                ),
                              ],
                            ),
                            if (_discountError != null) ...[
                              const SizedBox(height: 12),
                              Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: AppThemes.errorColor.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(color: AppThemes.errorColor.withValues(alpha: 0.3)),
                                ),
                                child: Row(
                                  children: [
                                    Icon(Icons.error_outline, color: AppThemes.errorColor, size: 16),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Text(
                                        _discountError!,
                                        style: TextStyle(
                                          color: AppThemes.errorColor,
                                          fontSize: 14,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                            if (_discountApplied && _discountDetails != null) ...[
                              const SizedBox(height: 12),
                              Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: AppThemes.successColor.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(color: AppThemes.successColor.withValues(alpha: 0.3)),
                                ),
                                child: Row(
                                  children: [
                                    Icon(Icons.check_circle_outline, color: AppThemes.successColor, size: 16),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Text(
                                        'Discount applied: ${_discountDetails!['code']} - Save \$${_discountDetails!['discountAmount'].toStringAsFixed(2)}',
                                        style: TextStyle(
                                          color: AppThemes.successColor,
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),

                      const SizedBox(height: 32),

                      // Pay Button (hidden for offline payments as they have their own submit button)
                      if (_selectedPaymentMethod != 'offline')
                        SizedBox(
                          width: double.infinity,
                          child: AuthButton(
                            label: widget.isProcessingPayment
                                ? 'Processing...'
                                : 'Complete Payment (\$${_getFinalAmount().toStringAsFixed(2)})',
                            onPressed: (widget.isProcessingPayment || _selectedPaymentMethod == null)
                                ? null
                                : _handlePayment,
                            isLoading: widget.isProcessingPayment,
                            icon: widget.isProcessingPayment
                                ? null
                                : const Icon(Icons.payment_outlined),
                          ),
                        ),

                      const SizedBox(height: 16),

                      // Security Info
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: AppThemes.infoColor.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: AppThemes.infoColor.withValues(alpha: 0.3)),
                        ),
                        child: Row(
                          children: [
                            Icon(Icons.security, color: AppThemes.infoColor, size: 20),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                'Your payment information is secure and encrypted.',
                                style: TextStyle(
                                  color: AppThemes.infoColor,
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
} 