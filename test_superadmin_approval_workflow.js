/**
 * 🔐 SuperAdmin Approval Workflow End-to-End Test
 * 
 * This script tests the complete offline payment approval workflow:
 * 1. User submits offline payment during registration
 * 2. System sends email notification to SuperAdmin
 * 3. SuperAdmin approves the payment
 * 4. User account gets activated
 * 5. User can login successfully
 */

const axios = require('axios');
const mongoose = require('mongoose');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

// Configuration
const BASE_URL = 'http://localhost:5000/api';
const SUPERADMIN_EMAIL = '<EMAIL>';
const SUPERADMIN_PASSWORD = 'SuperAdmin123!';

// Test data
const testUser = {
  fullName: 'Ahmed Hassan Test',
  email: `test.offline.${Date.now()}@example.com`,
  phone: '+************',
  password: 'TestPassword123!',
  shopName: 'Ahmed Electronics Test',
  shopAddress: 'Mogadishu, Somalia'
};

let testResults = {
  registration: false,
  emailVerification: false,
  offlinePaymentSubmission: false,
  superAdminNotification: false,
  superAdminApproval: false,
  accountActivation: false,
  userLogin: false
};

let testData = {
  userId: null,
  shopId: null,
  accessToken: null,
  superAdminToken: null,
  verificationCode: null
};

console.log('🧪 Starting SuperAdmin Approval Workflow Test\n');

/**
 * Step 1: Register user with offline payment
 */
async function testRegistration() {
  try {
    console.log('📝 Step 1: Testing user registration with offline payment...');
    
    const response = await axios.post(`${BASE_URL}/register`, {
      ...testUser,
      planType: 'monthly',
      paymentMethod: 'offline',
      initialPaid: false
    });

    if (response.data.success) {
      testData.userId = response.data.data.user.userId;
      testData.shopId = response.data.data.user.shopId;
      testResults.registration = true;
      
      console.log('✅ Registration successful');
      console.log(`   User ID: ${testData.userId}`);
      console.log(`   Shop ID: ${testData.shopId}`);
      console.log(`   Next Step: ${response.data.data.registrationProgress.nextStep}`);
      
      return true;
    }
  } catch (error) {
    console.log('❌ Registration failed:', error.response?.data?.message || error.message);
    return false;
  }
}

/**
 * Step 2: Verify email (simulate email verification)
 */
async function testEmailVerification() {
  try {
    console.log('\n📧 Step 2: Testing email verification...');
    
    // For testing, we'll use a mock verification code
    // In real scenario, this would come from email
    const mockVerificationCode = '123456';
    
    const response = await axios.post(`${BASE_URL}/verify-email`, {
      email: testUser.email,
      verificationCode: mockVerificationCode
    });

    if (response.data.success) {
      testData.accessToken = response.data.data.token?.accessToken;
      testResults.emailVerification = true;
      
      console.log('✅ Email verification successful');
      console.log(`   Access Token: ${testData.accessToken ? 'Generated' : 'Not generated'}`);
      console.log(`   Next Step: ${response.data.data.registrationProgress?.nextStep}`);
      
      return true;
    }
  } catch (error) {
    console.log('❌ Email verification failed:', error.response?.data?.message || error.message);
    
    // If verification fails due to invalid code, let's try to get the real code
    if (error.response?.status === 400) {
      console.log('   Attempting to find verification code in database...');
      return await findVerificationCodeInDB();
    }
    
    return false;
  }
}

/**
 * Helper: Find verification code in database
 */
async function findVerificationCodeInDB() {
  try {
    // Connect to MongoDB to get the verification code
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/deyncare');
    
    const User = require('./src/models/user.model');
    const user = await User.findOne({ email: testUser.email });
    
    if (user && user.emailVerification.code) {
      console.log(`   Found verification code: ${user.emailVerification.code}`);
      
      const response = await axios.post(`${BASE_URL}/verify-email`, {
        email: testUser.email,
        verificationCode: user.emailVerification.code
      });

      if (response.data.success) {
        testData.accessToken = response.data.data.token?.accessToken;
        testResults.emailVerification = true;
        
        console.log('✅ Email verification successful with DB code');
        return true;
      }
    }
    
    return false;
  } catch (error) {
    console.log('❌ Failed to get verification code from DB:', error.message);
    return false;
  }
}

/**
 * Step 3: Submit offline payment
 */
async function testOfflinePaymentSubmission() {
  try {
    console.log('\n💳 Step 3: Testing offline payment submission...');
    
    if (!testData.accessToken) {
      console.log('❌ No access token available for payment submission');
      return false;
    }

    const response = await axios.post(`${BASE_URL}/register/pay`, {
      planType: 'monthly',
      paymentMethod: 'offline',
      payerName: testUser.fullName,
      payerPhone: testUser.phone,
      notes: 'Test offline payment submission for SuperAdmin approval workflow'
    }, {
      headers: {
        'Authorization': `Bearer ${testData.accessToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.data.success) {
      testResults.offlinePaymentSubmission = true;
      
      console.log('✅ Offline payment submission successful');
      console.log(`   Next Step: ${response.data.data.registrationProgress?.nextStep}`);
      
      return true;
    }
  } catch (error) {
    console.log('❌ Offline payment submission failed:', error.response?.data?.message || error.message);
    return false;
  }
}

/**
 * Step 4: Login as SuperAdmin
 */
async function loginSuperAdmin() {
  try {
    console.log('\n🔐 Step 4: Logging in as SuperAdmin...');
    
    const response = await axios.post(`${BASE_URL}/login`, {
      email: SUPERADMIN_EMAIL,
      password: SUPERADMIN_PASSWORD
    });

    if (response.data.success) {
      testData.superAdminToken = response.data.data.token.accessToken;
      
      console.log('✅ SuperAdmin login successful');
      console.log(`   SuperAdmin Token: ${testData.superAdminToken ? 'Generated' : 'Not generated'}`);
      
      return true;
    }
  } catch (error) {
    console.log('❌ SuperAdmin login failed:', error.response?.data?.message || error.message);
    console.log('   Make sure SuperAdmin account exists with correct credentials');
    return false;
  }
}

/**
 * Step 5: Test SuperAdmin approval
 */
async function testSuperAdminApproval() {
  try {
    console.log('\n👑 Step 5: Testing SuperAdmin approval...');
    
    if (!testData.superAdminToken || !testData.shopId) {
      console.log('❌ Missing SuperAdmin token or Shop ID');
      return false;
    }

    const approvalData = {
      approvalNotes: 'Test approval - Offline payment verified via automated test',
      activateImmediately: true,
      confirmOfflinePayment: true,
      offlinePaymentDetails: {
        receiptNumber: `TEST-${Date.now()}`,
        paymentDate: new Date().toISOString(),
        amount: 50.00,
        currency: 'USD',
        paymentMethod: 'offline',
        notes: 'Payment verified via automated testing',
        verifiedBy: '<EMAIL>'
      }
    };

    const response = await axios.post(`${BASE_URL}/register/admin/approve-shop/${testData.shopId}`, approvalData, {
      headers: {
        'Authorization': `Bearer ${testData.superAdminToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.data.success) {
      testResults.superAdminApproval = true;
      
      console.log('✅ SuperAdmin approval successful');
      console.log(`   Shop Status: ${response.data.data.shop?.status}`);
      console.log(`   Account Activated: ${response.data.data.shop?.access?.isActivated}`);
      console.log(`   Payment Confirmed: ${response.data.data.shop?.access?.isPaid}`);
      
      return true;
    }
  } catch (error) {
    console.log('❌ SuperAdmin approval failed:', error.response?.data?.message || error.message);
    return false;
  }
}

/**
 * Step 6: Verify account activation
 */
async function testAccountActivation() {
  try {
    console.log('\n🔓 Step 6: Testing account activation...');
    
    if (!testData.superAdminToken || !testData.shopId) {
      console.log('❌ Missing required data for activation check');
      return false;
    }

    // Check shop status
    const response = await axios.get(`${BASE_URL}/shops/${testData.shopId}`, {
      headers: {
        'Authorization': `Bearer ${testData.superAdminToken}`
      }
    });

    if (response.data.success) {
      const shop = response.data.data;
      const isActivated = shop.access?.isActivated;
      const isPaid = shop.access?.isPaid;
      const status = shop.status;
      
      testResults.accountActivation = isActivated && isPaid && status === 'active';
      
      console.log('✅ Account activation check completed');
      console.log(`   Shop Status: ${status}`);
      console.log(`   Is Activated: ${isActivated}`);
      console.log(`   Is Paid: ${isPaid}`);
      console.log(`   Activation Result: ${testResults.accountActivation ? 'SUCCESS' : 'FAILED'}`);
      
      return testResults.accountActivation;
    }
  } catch (error) {
    console.log('❌ Account activation check failed:', error.response?.data?.message || error.message);
    return false;
  }
}

/**
 * Step 7: Test user login after approval
 */
async function testUserLogin() {
  try {
    console.log('\n🔑 Step 7: Testing user login after approval...');
    
    const response = await axios.post(`${BASE_URL}/login`, {
      email: testUser.email,
      password: testUser.password
    });

    if (response.data.success) {
      testResults.userLogin = true;
      
      console.log('✅ User login successful after approval');
      console.log(`   User Status: ${response.data.data.user?.status}`);
      console.log(`   Shop Access: ${response.data.data.user?.shopAccess ? 'Granted' : 'Denied'}`);
      
      return true;
    }
  } catch (error) {
    console.log('❌ User login failed:', error.response?.data?.message || error.message);
    return false;
  }
}

/**
 * Main test execution
 */
async function runCompleteWorkflowTest() {
  console.log('🚀 Starting Complete SuperAdmin Approval Workflow Test');
  console.log('=' .repeat(60));
  
  try {
    // Execute test steps
    const step1 = await testRegistration();
    if (!step1) return;
    
    const step2 = await testEmailVerification();
    if (!step2) return;
    
    const step3 = await testOfflinePaymentSubmission();
    if (!step3) return;
    
    const step4 = await loginSuperAdmin();
    if (!step4) return;
    
    const step5 = await testSuperAdminApproval();
    if (!step5) return;
    
    const step6 = await testAccountActivation();
    if (!step6) return;
    
    const step7 = await testUserLogin();
    
    // Print final results
    console.log('\n' + '=' .repeat(60));
    console.log('📊 FINAL TEST RESULTS');
    console.log('=' .repeat(60));
    
    Object.entries(testResults).forEach(([test, result]) => {
      const status = result ? '✅ PASS' : '❌ FAIL';
      console.log(`${status} ${test.replace(/([A-Z])/g, ' $1').toLowerCase()}`);
    });
    
    const totalTests = Object.keys(testResults).length;
    const passedTests = Object.values(testResults).filter(Boolean).length;
    
    console.log('\n📈 SUMMARY:');
    console.log(`   Total Tests: ${totalTests}`);
    console.log(`   Passed: ${passedTests}`);
    console.log(`   Failed: ${totalTests - passedTests}`);
    console.log(`   Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    
    if (passedTests === totalTests) {
      console.log('\n🎉 ALL TESTS PASSED! SuperAdmin approval workflow is working correctly.');
    } else {
      console.log('\n⚠️  Some tests failed. Please review the workflow implementation.');
    }
    
  } catch (error) {
    console.log('\n💥 Test execution failed:', error.message);
  } finally {
    // Cleanup
    if (mongoose.connection.readyState === 1) {
      await mongoose.disconnect();
    }
  }
}

// Run the test
if (require.main === module) {
  runCompleteWorkflowTest().catch(console.error);
}

module.exports = {
  runCompleteWorkflowTest,
  testResults,
  testData
};
