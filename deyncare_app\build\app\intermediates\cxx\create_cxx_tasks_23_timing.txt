# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 113ms
    create-variant-model 46ms
    create-ARMEABI_V7A-model 125ms
    create-ARM64_V8A-model 42ms
    create-X86-model 52ms
    create-X86_64-model 30ms
    create-module-model 16ms
    create-variant-model 16ms
    create-ARMEABI_V7A-model 70ms
    create-ARM64_V8A-model 84ms
    create-X86-model 149ms
    create-X86_64-model 41ms
    create-module-model
      [gap of 11ms]
      create-ndk-meta-abi-list 16ms
    create-module-model completed in 34ms
    create-variant-model 16ms
    create-ARMEABI_V7A-model 38ms
    create-ARM64_V8A-model 16ms
    create-X86-model 28ms
    create-X86_64-model 30ms
  create-initial-cxx-model completed in 990ms
  [gap of 101ms]
create_cxx_tasks completed in 1105ms

