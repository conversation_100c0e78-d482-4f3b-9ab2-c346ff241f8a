import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';
import 'package:deyncare_app/data/services/connectivity_service.dart';

/// The current state of connectivity with additional context
enum ConnectivityState {
  connected,
  disconnected,
  reconnected, // Special state when connection is restored
  unknown
}

/// Manages connectivity throughout the app with advanced features
class ConnectivityManager {
  // Singleton pattern
  static final ConnectivityManager _instance = ConnectivityManager._internal();
  factory ConnectivityManager() => _instance;
  ConnectivityManager._internal();
  
  // Services
  final ConnectivityService _connectivityService = ConnectivityService();
  
  // Stream controllers
  final _connectivityStateController = StreamController<ConnectivityState>.broadcast();
  Stream<ConnectivityState> get stateStream => _connectivityStateController.stream;
  
  // Tracking variables
  ConnectivityState _currentState = ConnectivityState.unknown;
  DateTime? _lastOfflineTime;
  Timer? _debounceTimer;
  Timer? _reachabilityCheckTimer;
  bool _initialized = false;
  int _consecutiveFailures = 0;
  DateTime? _lastSuccessfulCheck;
  bool _isStartupPhase = true;
  
  // Configuration
  Duration _debounceTime = const Duration(milliseconds: 500);
  Duration _reachabilityCheckInterval = const Duration(seconds: 60); // Increased from 30s
  static const Duration _maxCheckInterval = Duration(minutes: 5);
  static const Duration _minCheckInterval = Duration(seconds: 30); // Increased from 15s
  
  /// Initialize the connectivity manager
  Future<void> initialize({
    Duration? debounceTime, 
    Duration? reachabilityCheckInterval
  }) async {
    if (_initialized) return;
    
    _debounceTime = debounceTime ?? _debounceTime;
    _reachabilityCheckInterval = reachabilityCheckInterval ?? _reachabilityCheckInterval;
    
    // Initialize the connectivity service
    await _connectivityService.initialize();
    
    // Get initial status
    _updateStateFromStatus(_connectivityService.currentStatus);
    
    // Listen for connectivity changes with debounce
    _connectivityService.statusStream.listen(_debouncedStatusUpdate);
    
    // Enable periodic reachability checks for production, disable for development
    if (kReleaseMode) {
      // Delay startup checks to allow app to fully initialize
      Future.delayed(const Duration(seconds: 60), () {
        if (_initialized) {
          _isStartupPhase = false;
          _startReachabilityCheck();
        }
      });
      debugPrint('🔄 Reachability checks ENABLED for production (delayed startup)');
    } else {
      debugPrint('🔄 Reachability checks DISABLED for development (improved performance)');
    }
    
    _initialized = true;
  }

  /// Update state with debouncing to prevent rapid changes
  void _debouncedStatusUpdate(ConnectivityStatus status) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(_debounceTime, () {
      _updateStateFromStatus(status);
    });
  }
  
  /// Convert ConnectivityStatus to ConnectivityState with reconnection detection
  void _updateStateFromStatus(ConnectivityStatus status) {
    ConnectivityState newState;
    
    // Determine the new state based on connectivity status
    if (status == ConnectivityStatus.wifi || 
        status == ConnectivityStatus.mobile || 
        status == ConnectivityStatus.ethernet) {
      
      // Check if this is a reconnection event
      if (_currentState == ConnectivityState.disconnected) {
        newState = ConnectivityState.reconnected;
      } else {
        newState = ConnectivityState.connected;
      }
      
      // Reset offline time
      _lastOfflineTime = null;
    } else if (status == ConnectivityStatus.disconnected) {
      newState = ConnectivityState.disconnected;
      
      // Record offline time if not already set
      _lastOfflineTime ??= DateTime.now();
    } else {
      newState = ConnectivityState.unknown;
    }
    
    // Only notify if state actually changed
    if (newState != _currentState) {
      _currentState = newState;
      _connectivityStateController.add(newState);
      
      // For logging purposes
      debugPrint('ConnectivityManager: State changed to $_currentState');
    }
  }
  
  /// Start periodic internet reachability check
  void _startReachabilityCheck() {
    _reachabilityCheckTimer?.cancel();
    
    // Use adaptive interval based on recent success/failure pattern
    Duration interval = _calculateAdaptiveInterval();
    
    _reachabilityCheckTimer = Timer.periodic(interval, (_) {
      _performReachabilityCheck();
    });
    
    debugPrint('🔄 Started reachability check with ${interval.inSeconds}s interval');
  }
  
  /// Calculate adaptive check interval based on recent connectivity patterns
  Duration _calculateAdaptiveInterval() {
    // If we've had recent failures, check more frequently
    if (_consecutiveFailures > 0) {
      final failureMultiplier = (_consecutiveFailures * 0.5).clamp(0.5, 3.0);
      final adaptedInterval = Duration(
        seconds: (_minCheckInterval.inSeconds * failureMultiplier).round(),
      );
      return adaptedInterval.compareTo(_maxCheckInterval) > 0 
          ? _maxCheckInterval 
          : adaptedInterval;
    }
    
    // If we've been stable for a while, check less frequently
    if (_lastSuccessfulCheck != null) {
      final timeSinceSuccess = DateTime.now().difference(_lastSuccessfulCheck!);
      if (timeSinceSuccess > const Duration(minutes: 10)) {
        return _maxCheckInterval;
      }
    }
    
    return _reachabilityCheckInterval;
  }
  
  /// Perform reachability check with retry logic and grace period
  Future<void> _performReachabilityCheck() async {
    // Skip check if we're already in unknown state or recently changed
    if (_currentState == ConnectivityState.unknown) {
      return;
    }
    
    // Skip checks during startup phase to reduce noise
    if (_isStartupPhase) {
      debugPrint('⏭️ Skipping reachability check during startup phase');
      return;
    }
    
    // Add timeout for the entire reachability check process
    try {
      await _performReachabilityCheckWithTimeout();
    } catch (e) {
      debugPrint('❌ Reachability check timed out or failed: $e');
      // Don't update state on timeout - just continue with current state
      // This prevents blocking app startup due to network issues
    }
  }

  /// Perform reachability check with overall timeout
  Future<void> _performReachabilityCheckWithTimeout() async {
    // Wrap the entire check process in a timeout
    await _performActualReachabilityCheck().timeout(
      const Duration(seconds: 10), // Reduced timeout for faster checks
    );
  }

  /// Perform the actual reachability check
  Future<void> _performActualReachabilityCheck() async {
    // Perform the internet reachability check with retry
    bool hasInternet = false;
    int retryCount = 0;
    const maxRetries = 1; // Reduced from 2 to 1 for faster startup
    
    while (retryCount <= maxRetries && !hasInternet) {
      try {
        // Enable full checks for production, simplified for development
        if (kReleaseMode) {
          hasInternet = await checkInternetReachability(); // Full checks for production
        } else {
          hasInternet = true; // Assume connected during development for faster startup
        }
        if (!hasInternet && retryCount < maxRetries) {
          // Wait before retry - shorter delay
          await Future.delayed(Duration(seconds: 1 * (retryCount + 1)));
          retryCount++;
          debugPrint('🔄 Retrying internet check (attempt ${retryCount + 1}/${maxRetries + 1})');
        } else {
          break;
        }
      } catch (e) {
        debugPrint('❌ Reachability check error: $e');
        retryCount++;
        if (retryCount <= maxRetries) {
          await Future.delayed(Duration(seconds: 1 * retryCount));
        }
      }
    }
    
    await _handleReachabilityResult(hasInternet);
  }

  /// Handle the result of reachability check
  Future<void> _handleReachabilityResult(bool hasInternet) async {
    // Apply state changes with grace period logic
    if (!hasInternet && _currentState == ConnectivityState.connected) {
      // We have network connectivity but no internet - apply grace period
      debugPrint('⚠️ Internet unreachable but network connected - applying grace period');
      
      // Check again after a short grace period before marking as offline
      await Future.delayed(const Duration(seconds: 3)); // Reduced from 5s
      
      // Double-check internet connectivity after grace period
      // For startup phase, assume connected to avoid false negatives
      final doubleCheckResult = _isStartupPhase ? true : await checkInternetReachability();
      if (!doubleCheckResult) {
        debugPrint('📴 Confirmed: Internet unreachable after grace period');
        _consecutiveFailures++;
        _updateStateFromStatus(ConnectivityStatus.disconnected);
        
        // Restart timer with adaptive interval
        _startReachabilityCheck();
      } else {
        debugPrint('✅ Internet restored during grace period');
        _consecutiveFailures = 0;
        _lastSuccessfulCheck = DateTime.now();
      }
    } else if (hasInternet && _currentState == ConnectivityState.disconnected) {
      // We have internet but state shows disconnected - restore connection
      debugPrint('🔄 Internet restored, updating state');
      _consecutiveFailures = 0;
      _lastSuccessfulCheck = DateTime.now();
      
      // Determine the appropriate connected state based on current network type
      final connectivityResult = await Connectivity().checkConnectivity();
      if (connectivityResult.isNotEmpty) {
        switch (connectivityResult.first) {
          case ConnectivityResult.wifi:
            _updateStateFromStatus(ConnectivityStatus.wifi);
            break;
          case ConnectivityResult.mobile:
            _updateStateFromStatus(ConnectivityStatus.mobile);
            break;
          case ConnectivityResult.ethernet:
            _updateStateFromStatus(ConnectivityStatus.ethernet);
            break;
          default:
            _updateStateFromStatus(ConnectivityStatus.wifi); // Default fallback
        }
      }
      
      // Restart timer with adaptive interval
      _startReachabilityCheck();
    } else if (hasInternet) {
      // Update success tracking even if state didn't change
      _consecutiveFailures = 0;
      _lastSuccessfulCheck = DateTime.now();
    }
  }

  /// Check if we have actual internet access, not just network connectivity
  /// ENABLED for production to ensure proper connectivity validation
  Future<bool> checkInternetReachability() async {
    // More reliable endpoints with better fallback strategy
    final List<String> testEndpoints = [
      'https://httpbin.org/status/200',      // Simple HTTPS endpoint
      'https://www.google.com/generate_204', // Google's connectivity check endpoint
      'https://www.cloudflare.com/cdn-cgi/trace', // Cloudflare's endpoint
    ];
    
    int successCount = 0;
    int requiredSuccesses = 1; // Only need one success to consider online
    
    // Create Dio instance for connectivity checks
    final dio = Dio();
    
    // Try multiple endpoints concurrently with different timeouts
    final List<Future<bool>> checks = testEndpoints.map((endpoint) async {
      try {
        // Use shorter timeouts for faster checks
        final timeout = const Duration(seconds: 3);
        
        // Configure Dio with timeout
        dio.options = BaseOptions(
          connectTimeout: timeout,
          receiveTimeout: timeout,
          sendTimeout: timeout,
        );
        
        final response = await dio.get(
          endpoint,
          options: Options(
            headers: {
              'User-Agent': 'DeynCare-Mobile/1.0',
              'Cache-Control': 'no-cache',
            },
          ),
        );
        
        // Accept various success status codes
        final isSuccess = response.statusCode != null && 
                         response.statusCode! >= 200 && 
                         response.statusCode! < 400;
        
        if (isSuccess) {
          debugPrint('✅ Internet check SUCCESS: $endpoint (${response.statusCode})');
        } else {
          debugPrint('⚠️ Internet check FAILED: $endpoint (${response.statusCode})');
        }
        
        return isSuccess;
      } catch (e) {
        // Only log errors if not during startup to reduce noise
        if (!_isStartupPhase) {
          debugPrint('❌ Internet check ERROR: $endpoint - $e');
        }
        return false;
      }
    }).toList();
    
    // Wait for all checks to complete or until we get enough successes
    try {
      final results = await Future.wait(checks, eagerError: false);
      successCount = results.where((result) => result).length;
      
      final isOnline = successCount >= requiredSuccesses;
      
      // Only log detailed results if not during startup
      if (!_isStartupPhase) {
        debugPrint('🌐 Internet reachability: $isOnline ($successCount/${testEndpoints.length} endpoints succeeded)');
      }
      
      return isOnline;
    } catch (e) {
      if (!_isStartupPhase) {
        debugPrint('❌ Internet reachability check failed: $e');
      }
      return false;
    } finally {
      // Close the Dio instance to free resources
      dio.close();
    }
  }
  
  /// Get the current connectivity state
  ConnectivityState get currentState => _currentState;
  
  /// Check if device is currently connected
  bool isConnected() {
    return _currentState == ConnectivityState.connected || 
           _currentState == ConnectivityState.reconnected;
  }
  
  /// Get duration of current offline period
  Duration? getOfflineDuration() {
    if (_lastOfflineTime == null) return null;
    return DateTime.now().difference(_lastOfflineTime!);
  }
  
  /// Get connection quality based on recent performance
  ConnectionQuality getConnectionQuality() {
    if (!isConnected()) return ConnectionQuality.offline;
    
    if (_consecutiveFailures == 0 && _lastSuccessfulCheck != null) {
      final timeSinceSuccess = DateTime.now().difference(_lastSuccessfulCheck!);
      if (timeSinceSuccess < const Duration(minutes: 2)) {
        return ConnectionQuality.excellent;
      } else if (timeSinceSuccess < const Duration(minutes: 5)) {
        return ConnectionQuality.good;
      }
    }
    
    if (_consecutiveFailures > 3) return ConnectionQuality.poor;
    if (_consecutiveFailures > 1) return ConnectionQuality.fair;
    
    return ConnectionQuality.good;
  }
  
  /// Get detailed connectivity status information
  Future<ConnectivityInfo> getConnectivityInfo() async {
    final networkDetails = await getNetworkDetails();
    final quality = getConnectionQuality();
    final offlineDuration = getOfflineDuration();
    
    return ConnectivityInfo(
      state: _currentState,
      networkDetails: networkDetails,
      quality: quality,
      offlineDuration: offlineDuration,
      consecutiveFailures: _consecutiveFailures,
      lastSuccessfulCheck: _lastSuccessfulCheck,
    );
  }
  
  /// Get detailed network information
  Future<String> getNetworkDetails() async {
    return _connectivityService.getNetworkDetails();
  }
  
  /// Schedule an operation to retry when connection is restored
  /// Returns a future that completes when the operation succeeds
  Future<T> scheduleRetryWhenOnline<T>(Future<T> Function() operation) async {
    // If already online, just execute
    if (isConnected()) {
      try {
        return await operation();
      } catch (e) {
        // If operation fails, it might be a momentary connection issue
        // Fall through to the waiting logic below
      }
    }
    
    // Wait for connectivity
    Completer<T> completer = Completer<T>();
    StreamSubscription? subscription;
    
    subscription = stateStream.listen((state) async {
      if (state == ConnectivityState.connected || 
          state == ConnectivityState.reconnected) {
        // Connection is available, try the operation
        try {
          final result = await operation();
          if (!completer.isCompleted) {
            completer.complete(result);
            subscription?.cancel();
          }
        } catch (e) {
          // Operation failed, but we'll keep the subscription active
          // for the next connectivity change
          debugPrint('Retry operation failed: $e');
        }
      }
    });
    
    return completer.future;
  }
  
  /// Dispose resources
  void dispose() {
    _debounceTimer?.cancel();
    _reachabilityCheckTimer?.cancel();
    _connectivityStateController.close();
  }
}

/// Connection quality levels
enum ConnectionQuality {
  offline,
  poor,
  fair,
  good,
  excellent,
}

/// Detailed connectivity information
class ConnectivityInfo {
  final ConnectivityState state;
  final String networkDetails;
  final ConnectionQuality quality;
  final Duration? offlineDuration;
  final int consecutiveFailures;
  final DateTime? lastSuccessfulCheck;
  
  ConnectivityInfo({
    required this.state,
    required this.networkDetails,
    required this.quality,
    this.offlineDuration,
    required this.consecutiveFailures,
    this.lastSuccessfulCheck,
  });
  
  @override
  String toString() {
    return 'ConnectivityInfo(state: $state, quality: $quality, '
           'failures: $consecutiveFailures, offline: $offlineDuration)';
  }
}
