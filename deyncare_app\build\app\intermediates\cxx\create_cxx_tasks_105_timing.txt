# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 15ms
    create-variant-model 16ms
    create-ARMEABI_V7A-model 30ms
    create-ARM64_V8A-model 14ms
    create-X86-model 10ms
    create-X86_64-model 11ms
    create-module-model 15ms
    [gap of 28ms]
    create-X86-model 11ms
    [gap of 19ms]
    create-variant-model 10ms
    create-ARMEABI_V7A-model 10ms
    [gap of 18ms]
    create-X86_64-model 12ms
  create-initial-cxx-model completed in 227ms
  [gap of 69ms]
create_cxx_tasks completed in 299ms

