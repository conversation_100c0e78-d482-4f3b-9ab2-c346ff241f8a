package com.deyncare.app

import android.content.Intent
import android.net.Uri
import androidx.core.content.FileProvider
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import java.io.File

class MainActivity: FlutterActivity() {
    private val CHANNEL = "com.deyncare.app/file_provider"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "openPDF" -> {
                    val filePath = call.argument<String>("filePath")
                    if (filePath != null) {
                        try {
                            openPDFWithFileProvider(filePath)
                            result.success("PDF opened successfully")
                        } catch (e: Exception) {
                            result.error("OPEN_ERROR", "Failed to open PDF: ${e.message}", null)
                        }
                    } else {
                        result.error("INVALID_ARGUMENT", "File path is required", null)
                    }
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }

    private fun openPDFWithFileProvider(filePath: String) {
        try {
            val file = File(filePath)
            if (!file.exists()) {
                throw Exception("File does not exist: $filePath")
            }

            if (!file.canRead()) {
                throw Exception("Cannot read file: $filePath")
            }

            // Verify file is not empty
            if (file.length() == 0L) {
                throw Exception("PDF file is empty or corrupted")
            }

            val uri: Uri = FileProvider.getUriForFile(
                this,
                "${packageName}.fileprovider",
                file
            )

            val intent = Intent(Intent.ACTION_VIEW).apply {
                setDataAndType(uri, "application/pdf")
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_GRANT_READ_URI_PERMISSION
                addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
            }

            // Check if there's an app that can handle PDF files
            val resolveInfo = intent.resolveActivity(packageManager)
            if (resolveInfo != null) {
                try {
                    // Grant URI permissions to all apps that can handle the intent
                    val resInfoList = packageManager.queryIntentActivities(intent, 0)
                    for (resolveInfo in resInfoList) {
                        val packageName = resolveInfo.activityInfo.packageName
                        grantUriPermission(packageName, uri, Intent.FLAG_GRANT_READ_URI_PERMISSION)
                    }

                    startActivity(intent)
                } catch (e: Exception) {
                    throw Exception("Failed to start PDF viewer: ${e.message}")
                }
            } else {
                throw Exception("No PDF viewer app found. Please install a PDF reader app from the Play Store.")
            }
        } catch (e: SecurityException) {
            throw Exception("Security error accessing file: ${e.message}")
        } catch (e: IllegalArgumentException) {
            throw Exception("Invalid file path or FileProvider configuration error: ${e.message}")
        } catch (e: Exception) {
            throw Exception("Error opening PDF: ${e.message}")
        }
    }
}
