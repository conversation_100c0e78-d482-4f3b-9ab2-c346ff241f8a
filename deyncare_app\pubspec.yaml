name: deyncare_app
description: "Deyncare Mobile Application for Debt & POS Management"
publish_to: 'none' # Remove this line if you wish to publish to pub.dev
version: 1.0.0+1

environment:
  sdk: ^3.6.0

dependencies:
  flutter:
    sdk: flutter

  # UI Components
  cupertino_icons: ^1.0.8
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.1
  shimmer: ^3.0.0
  lottie: ^3.0.0
  intl_phone_field: ^3.2.0
  syncfusion_flutter_charts: ^29.2.10
  animated_custom_dropdown: ^3.1.1
  awesome_dialog: ^3.2.1
  wolt_modal_sheet: ^0.11.0
  flutter_card_swiper: ^7.0.2
  # State Management
  flutter_bloc: ^8.1.4
  equatable: ^2.0.5
  get_it: ^7.6.7

  # Network & API
  dio: ^5.4.1
  connectivity_plus: ^6.1.4
  http: ^1.2.0

  # Firebase & Push Notifications
  firebase_core: ^2.24.2
  firebase_messaging: ^14.7.10
  firebase_analytics: ^10.7.4
  flutter_local_notifications: ^17.2.3
  device_info_plus: ^9.1.1
  package_info_plus: ^4.2.0

  # Local Storage
  shared_preferences: ^2.5.3
  flutter_secure_storage: ^9.0.0

  # Utilities
  dartz: ^0.10.1
  intl: ^0.19.0
  logger: ^2.5.0
  path_provider: ^2.1.2
  url_launcher: ^6.3.1
  fluttertoast: ^8.2.12
  json_annotation: ^4.8.1
  file_picker: ^6.1.1

  # Form Management
  formz: ^0.6.1
  email_validator: ^2.1.17

  # Image Handling
  image_picker: ^1.0.7
  app_links: ^3.4.1
  retry: ^3.1.2
 
  network_info_plus: ^6.1.4
  bloc_concurrency: ^0.2.5
  provider: ^6.1.5

  # PDF Generation
  pdf: ^3.10.4

  share_plus: ^7.2.1
  permission_handler: ^11.0.1
  
  # File handling
  open_file: ^3.3.2

dev_dependencies:
  # Core testing packages
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter
  
  # Unit testing utilities
  mockito: ^5.4.4
  mocktail: ^1.0.3
  bloc_test: ^9.1.5
  
  # Code quality and analysis
  flutter_lints: ^5.0.0
  very_good_analysis: ^5.1.0
  json_serializable: ^6.7.1
  build_runner: ^2.4.8
  
  # Test coverage
  coverage: ^1.7.2
  
  # Test utilities
  network_image_mock: ^2.1.1
  golden_toolkit: ^0.15.0
  
  # App icon generation
  flutter_launcher_icons: ^0.13.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/icons/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

# Flutter Launcher Icons Configuration
flutter_icons:
  # Android icons
  android: "launcher_icon"
  
  # iOS icons
  ios: true
  
  # Source image path - will be high-quality PNG generated from SVG
  image_path: "assets/icons/deyncare_app_icon_1024.png"
  
  # Minimum Android SDK version
  min_sdk_android: 21
  
  # Adaptive icon settings for Android 8.0+ (API 26+)
  adaptive_icon_background: "#FFFFFF"
  adaptive_icon_foreground: "assets/icons/deyncare_app_icon_1024.png"
  
  # Remove old launcher icons when generating new ones
  remove_alpha_ios: true
  
  # Generate for web (if you plan to use Flutter web)
  web:
    generate: true
    image_path: "assets/icons/deyncare_app_icon_1024.png"
    background_color: "#FFFFFF"
    theme_color: "#141DEE"
