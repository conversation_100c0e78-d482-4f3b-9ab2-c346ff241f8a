import 'dart:io';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/core/utils/toast_util.dart';
import 'package:deyncare_app/presentation/screens/auth/widgets/auth_text_field.dart';

/// Enhanced offline payment form with file upload capability
class OfflinePaymentForm extends StatefulWidget {
  final String planName;
  final double planAmount;
  final String planType;
  final String? initialPayerName;
  final String? initialPayerPhone;
  final Function(Map<String, dynamic>) onSubmit;
  final bool isLoading;

  const OfflinePaymentForm({
    super.key,
    required this.planName,
    required this.planAmount,
    required this.planType,
    this.initialPayerName,
    this.initialPayerPhone,
    required this.onSubmit,
    this.isLoading = false,
  });

  @override
  State<OfflinePaymentForm> createState() => _OfflinePaymentFormState();
}

class _OfflinePaymentFormState extends State<OfflinePaymentForm> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _payerNameController;
  late TextEditingController _payerPhoneController;
  late TextEditingController _notesController;
  
  File? _selectedFile;
  String? _fileName;
  bool _isUploading = false;

  @override
  void initState() {
    super.initState();
    _payerNameController = TextEditingController(text: widget.initialPayerName ?? '');
    _payerPhoneController = TextEditingController(text: widget.initialPayerPhone ?? '');
    _notesController = TextEditingController();
  }

  @override
  void dispose() {
    _payerNameController.dispose();
    _payerPhoneController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _pickPaymentProof() async {
    try {
      setState(() => _isUploading = true);
      
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['jpg', 'jpeg', 'png', 'pdf'],
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        final file = File(result.files.single.path!);
        final fileSize = await file.length();
        
        // Check file size (5MB limit)
        if (fileSize > 5 * 1024 * 1024) {
          ToastUtil.showError('File too large. Maximum size is 5MB.');
          return;
        }

        setState(() {
          _selectedFile = file;
          _fileName = result.files.single.name;
        });
        
        ToastUtil.showSuccess('Payment proof selected successfully');
      }
    } catch (e) {
      ToastUtil.showError('Failed to select file: ${e.toString()}');
    } finally {
      setState(() => _isUploading = false);
    }
  }

  void _removeSelectedFile() {
    setState(() {
      _selectedFile = null;
      _fileName = null;
    });
  }

  String? _validatePayerName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Payer name is required';
    }
    if (value.trim().length < 2) {
      return 'Payer name must be at least 2 characters';
    }
    if (value.trim().length > 100) {
      return 'Payer name must be less than 100 characters';
    }
    return null;
  }

  String? _validatePayerPhone(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Payer phone number is required';
    }
    // Basic phone validation for Somali numbers
    final phoneRegex = RegExp(r'^\+252[0-9]{8,9}$');
    if (!phoneRegex.hasMatch(value.trim())) {
      return 'Please enter a valid Somali phone number (+252XXXXXXXX)';
    }
    return null;
  }

  String? _validateNotes(String? value) {
    if (value != null && value.length > 1000) {
      return 'Notes must be less than 1000 characters';
    }
    return null;
  }

  void _handleSubmit() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final formData = {
      'payerName': _payerNameController.text.trim(),
      'payerPhone': _payerPhoneController.text.trim(),
      'notes': _notesController.text.trim(),
      'paymentProof': _selectedFile,
    };

    widget.onSubmit(formData);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AppThemes.cardColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppThemes.dividerColor),
      ),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.store,
                  color: AppThemes.primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'Offline Payment Details',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppThemes.textPrimaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Please provide your payment details for verification',
              style: TextStyle(
                fontSize: 14,
                color: AppThemes.textSecondaryColor,
              ),
            ),
            const SizedBox(height: 24),

            // Payer Name Field
            AuthTextField(
              label: 'Payer Name *',
              hintText: 'Enter the name of the person making payment',
              controller: _payerNameController,
              keyboardType: TextInputType.name,
              prefixIcon: const Icon(Icons.person_outline),
              validator: _validatePayerName,
            ),
            const SizedBox(height: 16),

            // Payer Phone Field
            AuthTextField(
              label: 'Payer Phone Number *',
              hintText: '+252612345678',
              controller: _payerPhoneController,
              keyboardType: TextInputType.phone,
              prefixIcon: const Icon(Icons.phone_outlined),
              validator: _validatePayerPhone,
            ),
            const SizedBox(height: 16),

            // Notes Field
            AuthTextField(
              label: 'Payment Notes',
              hintText: 'Add any additional payment details (optional)',
              controller: _notesController,
              keyboardType: TextInputType.multiline,
              maxLines: 3,
              prefixIcon: const Icon(Icons.note_outlined),
              validator: _validateNotes,
            ),
            const SizedBox(height: 24),

            // File Upload Section
            _buildFileUploadSection(),
            const SizedBox(height: 32),

            // Submit Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: (widget.isLoading || _isUploading) ? null : _handleSubmit,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppThemes.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: widget.isLoading
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(Icons.send_outlined, size: 20),
                          const SizedBox(width: 8),
                          Text(
                            'Submit Offline Payment (\$${widget.planAmount.toStringAsFixed(2)})',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFileUploadSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppThemes.successColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppThemes.successColor.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.upload_file_outlined,
                color: AppThemes.successColor,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Payment Proof (Optional)',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppThemes.successColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Upload a screenshot or photo of your payment receipt',
            style: TextStyle(
              fontSize: 14,
              color: AppThemes.textSecondaryColor,
            ),
          ),
          const SizedBox(height: 16),

          if (_selectedFile == null) ...[
            // Upload Button
            InkWell(
              onTap: _isUploading ? null : _pickPaymentProof,
              borderRadius: BorderRadius.circular(8),
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
                decoration: BoxDecoration(
                  color: AppThemes.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: AppThemes.primaryColor.withValues(alpha: 0.3),
                    style: BorderStyle.solid,
                  ),
                ),
                child: _isUploading
                    ? const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            height: 16,
                            width: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                          SizedBox(width: 12),
                          Text('Selecting file...'),
                        ],
                      )
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.cloud_upload_outlined,
                            color: AppThemes.primaryColor,
                            size: 20,
                          ),
                          const SizedBox(width: 12),
                          Text(
                            'Choose File',
                            style: TextStyle(
                              color: AppThemes.primaryColor,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Supported formats: JPG, PNG, PDF (Max 5MB)',
              style: TextStyle(
                fontSize: 12,
                color: AppThemes.textHintColor,
              ),
            ),
          ] else ...[
            // Selected File Display
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppThemes.successColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AppThemes.successColor.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.check_circle_outline,
                    color: AppThemes.successColor,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'File Selected',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: AppThemes.successColor,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          _fileName ?? 'Unknown file',
                          style: TextStyle(
                            fontSize: 12,
                            color: AppThemes.textSecondaryColor,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: _removeSelectedFile,
                    icon: Icon(
                      Icons.close,
                      color: AppThemes.errorColor,
                      size: 20,
                    ),
                    tooltip: 'Remove file',
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
}
