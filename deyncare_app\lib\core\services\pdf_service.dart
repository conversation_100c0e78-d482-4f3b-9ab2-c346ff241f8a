import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:url_launcher/url_launcher.dart';
import 'package:share_plus/share_plus.dart';
import 'package:deyncare_app/data/models/customer_report_model.dart';
import 'package:deyncare_app/data/models/debt_report_model.dart';
import 'package:deyncare_app/data/models/risk_report_model.dart';

/// PDF generation service with proper PDF creation and external storage
class PDFService {
  /// Generate Customer Report PDF with proper PDF format and external storage
  static Future<String> generateCustomerReport({
    required List<CustomerReportData> customers,
    required Map<String, dynamic> summary,
    required String reportPeriod,
    required String shopName,
    String? shopLogo,
  }) async {
    try {
      if (kDebugMode) {
        print('📊 Starting PDF generation for ${customers.length} customers');
      }

      // Request storage permission
      await _requestStoragePermission();

      // Create PDF document
      final pdf = pw.Document();

      // Add pages to PDF
      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(24),
          build: (pw.Context context) {
            return [
              _buildHeader(shopName, reportPeriod),
              pw.SizedBox(height: 20),
              _buildCustomerSummary(summary),
              pw.SizedBox(height: 20),
              ..._buildPaginatedCustomerTable(customers),
              pw.SizedBox(height: 20),
              _buildFooter(),
            ];
          },
        ),
      );

      // Save PDF to external storage
      final filePath = await _savePdfToExternalStorage(pdf);

      if (kDebugMode) {
        print('✅ PDF report generated successfully: $filePath');
      }

      return filePath;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error generating PDF report: $e');
      }
      rethrow;
    }
  }

  /// Request storage permission for external storage access
  static Future<void> _requestStoragePermission() async {
    try {
      final status = await Permission.storage.status;
      if (!status.isGranted) {
        final result = await Permission.storage.request();
        if (!result.isGranted) {
          throw Exception(
              'Storage permission denied. Cannot save PDF to external storage.');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Storage permission handling: $e');
      }
      // Continue without permission for internal storage fallback
    }
  }

  /// Save PDF to external storage in user-accessible location
  static Future<String> _savePdfToExternalStorage(pw.Document pdf) async {
    try {
      // Try to get external storage directory (Downloads or Documents)
      Directory? directory;

      // Try Downloads directory first
      if (Platform.isAndroid) {
        directory = Directory('/storage/emulated/0/Download/DeynCare');
      } else {
        // For iOS, use Documents directory
        directory = await getApplicationDocumentsDirectory();
        directory = Directory('${directory.path}/DeynCare');
      }

      // Create DeynCare folder if it doesn't exist
      if (!await directory.exists()) {
        await directory.create(recursive: true);
        if (kDebugMode) {
          print('📁 Created DeynCare folder: ${directory.path}');
        }
      }

      // Generate filename with timestamp
      final now = DateTime.now();
      final fileName =
          'Customer_Report_${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}_${now.hour.toString().padLeft(2, '0')}${now.minute.toString().padLeft(2, '0')}${now.second.toString().padLeft(2, '0')}.pdf';

      final file = File('${directory.path}/$fileName');

      // Save PDF bytes to file
      final pdfBytes = await pdf.save();
      await file.writeAsBytes(pdfBytes);

      if (kDebugMode) {
        print('💾 PDF saved to: ${file.path}');
        print('📄 File size: ${pdfBytes.length} bytes');
      }

      return file.path;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error saving to external storage: $e');
        print('🔄 Falling back to internal storage...');
      }

      // Fallback to internal storage
      return _savePdfToInternalStorage(pdf);
    }
  }

  /// Fallback: Save PDF to internal storage
  static Future<String> _savePdfToInternalStorage(pw.Document pdf) async {
    final directory = await getApplicationDocumentsDirectory();
    final reportsDir = Directory('${directory.path}/reports');

    if (!await reportsDir.exists()) {
      await reportsDir.create(recursive: true);
    }

    final now = DateTime.now();
    final fileName =
        'Customer_Report_${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}_${now.hour.toString().padLeft(2, '0')}${now.minute.toString().padLeft(2, '0')}${now.second.toString().padLeft(2, '0')}.pdf';

    final file = File('${reportsDir.path}/$fileName');
    final pdfBytes = await pdf.save();
    await file.writeAsBytes(pdfBytes);

    if (kDebugMode) {
      print('💾 PDF saved to internal storage: ${file.path}');
    }

    return file.path;
  }

  /// Build PDF header section
  static pw.Widget _buildHeader(String shopName, String reportPeriod) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'CUSTOMER REPORT',
          style: pw.TextStyle(
            fontSize: 24,
            fontWeight: pw.FontWeight.bold,
          ),
        ),
        pw.SizedBox(height: 10),
        pw.Text(
          'Shop: $shopName',
          style: pw.TextStyle(fontSize: 14),
        ),
        pw.Text(
          'Period: $reportPeriod',
          style: pw.TextStyle(fontSize: 14),
        ),
        pw.Text(
          'Generated: ${DateTime.now().toString().substring(0, 19)}',
          style: pw.TextStyle(fontSize: 12, color: PdfColors.grey),
        ),
        pw.Divider(thickness: 2),
      ],
    );
  }

  /// Build customer summary section
  static pw.Widget _buildCustomerSummary(Map<String, dynamic> summary) {
    if (kDebugMode) {
      print('🔍 [PDF DEBUG] Customer summary data:');
      print('   - Summary keys: ${summary.keys.toList()}');
      print('   - Total customers: ${summary['totalCustomers']}');
      print('   - Customer types: ${summary['customerTypes']}');
      print('   - Total debt amount: ${summary['totalDebtAmount']}');
      print('   - Total outstanding debt: ${summary['totalOutstandingDebt']}');
    }
    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey400),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'Customer Summary',
            style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
          ),
          pw.SizedBox(height: 10),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Expanded(
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                        'Total Customers: ${summary['totalCustomers'] ?? 0}'),
                    pw.Text(
                        'New Customers: ${_getCustomerTypeCount(summary, 'New')}'),
                  ],
                ),
              ),
              pw.Expanded(
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                        'Returning Customers: ${_getCustomerTypeCount(summary, 'Returning')}'),
                    pw.Text(
                        'Total Debt: \$${((summary['totalDebtAmount'] as num?)?.toDouble() ?? 0.0).toStringAsFixed(2)}'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build paginated customer table that can span multiple pages
  static List<pw.Widget> _buildPaginatedCustomerTable(
      List<CustomerReportData> customers) {
    if (customers.isEmpty) {
      return [
        pw.Container(
          padding: const pw.EdgeInsets.all(16),
          child: pw.Text('No customer data available for the selected period.'),
        ),
      ];
    }

    const int rowsPerPage =
        12; // Conservative limit to prevent TooManyPagesException
    final List<pw.Widget> widgets = [];

    // Add table header
    widgets.add(
      pw.Text(
        'Customer Details',
        style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold),
      ),
    );
    widgets.add(pw.SizedBox(height: 10));

    // Split customers into chunks for pagination
    for (int i = 0; i < customers.length; i += rowsPerPage) {
      final chunk = customers.skip(i).take(rowsPerPage).toList();

      widgets.add(
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.grey400),
          columnWidths: {
            0: const pw.FlexColumnWidth(2.0), // Customer Name
            1: const pw.FlexColumnWidth(1.2), // Customer Type
            2: const pw.FlexColumnWidth(1.3), // Total Debt
            3: const pw.FlexColumnWidth(1.3), // Outstanding
            4: const pw.FlexColumnWidth(1.2), // Risk Level
            5: const pw.FlexColumnWidth(1.2), // Created Date
          },
          children: [
            // Header row (only for first chunk)
            if (i == 0)
              pw.TableRow(
                decoration: const pw.BoxDecoration(color: PdfColors.grey200),
                children: [
                  _buildTableCell('Customer Name', isHeader: true),
                  _buildTableCell('Type', isHeader: true),
                  _buildTableCell('Total Debt', isHeader: true),
                  _buildTableCell('Outstanding', isHeader: true),
                  _buildTableCell('Risk Level', isHeader: true),
                  _buildTableCell('Created Date', isHeader: true),
                ],
              ),
            // Data rows for this chunk
            ...chunk.map((customer) => pw.TableRow(
                  children: [
                    _buildTableCell(customer.customerName),
                    _buildTableCell(customer.customerType),
                    _buildTableCell(
                        '\$${customer.totalDebt.toStringAsFixed(2)}'),
                    _buildTableCell(
                        '\$${customer.outstandingDebt.toStringAsFixed(2)}'),
                    _buildTableCell(customer.riskLevel),
                    _buildTableCell(
                        _formatDate(customer.createdAt.toIso8601String())),
                  ],
                )),
          ],
        ),
      );

      // Add spacing between table chunks
      if (i + rowsPerPage < customers.length) {
        widgets.add(pw.SizedBox(height: 20));
      }
    }

    return widgets;
  }

  /// Helper method to extract customer type count from summary
  static int _getCustomerTypeCount(
      Map<String, dynamic> summary, String customerType) {
    final customerTypes = summary['customerTypes'] as Map<String, dynamic>?;
    if (customerTypes == null) return 0;

    return (customerTypes[customerType] as num?)?.toInt() ?? 0;
  }

  /// Build PDF summary section (DEPRECATED - replaced by specific summary methods)
  static pw.Widget _buildSummary(Map<String, dynamic> summary) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey),
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'SUMMARY',
            style: pw.TextStyle(
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.SizedBox(height: 8),
          pw.Text('Total Customers: ${summary['totalCustomers'] ?? 0}'),
          pw.Text(
              'Total Debt Amount: \$${(summary['totalDebtAmount'] ?? 0).toStringAsFixed(2)}'),
          pw.Text(
              'Outstanding Debt: \$${(summary['totalOutstandingDebt'] ?? 0).toStringAsFixed(2)}'),
        ],
      ),
    );
  }

  /// Build PDF customer table
  static pw.Widget _buildCustomerTable(List<CustomerReportData> customers) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'CUSTOMER DETAILS',
          style: pw.TextStyle(
            fontSize: 16,
            fontWeight: pw.FontWeight.bold,
          ),
        ),
        pw.SizedBox(height: 10),
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.grey),
          columnWidths: {
            0: const pw.FlexColumnWidth(2), // Customer Name
            1: const pw.FlexColumnWidth(1), // Type
            2: const pw.FlexColumnWidth(2), // Phone
            3: const pw.FlexColumnWidth(1.5), // Risk Level
            4: const pw.FlexColumnWidth(1.5), // Total Debt
            5: const pw.FlexColumnWidth(1.5), // Outstanding
          },
          children: [
            // Header row
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.grey200),
              children: [
                _buildTableCell('Customer Name', isHeader: true),
                _buildTableCell('Type', isHeader: true),
                _buildTableCell('Phone', isHeader: true),
                _buildTableCell('Risk Level', isHeader: true),
                _buildTableCell('Total Debt', isHeader: true),
                _buildTableCell('Outstanding', isHeader: true),
              ],
            ),
            // Data rows
            ...customers.map((customer) => pw.TableRow(
                  children: [
                    _buildTableCell(customer.customerName),
                    _buildTableCell(customer.customerType),
                    _buildTableCell(customer.phone),
                    _buildTableCell(customer.riskLevel),
                    _buildTableCell(
                        '\$${customer.totalDebt.toStringAsFixed(2)}'),
                    _buildTableCell(
                        '\$${customer.outstandingDebt.toStringAsFixed(2)}'),
                  ],
                )),
          ],
        ),
      ],
    );
  }

  /// Build PDF customer table with column totals
  static pw.Widget _buildCustomerTableWithTotals(
      List<CustomerReportData> customers, Map<String, dynamic> summary) {
    if (kDebugMode) {
      print('🔍 [PDF DEBUG] Building table with ${customers.length} customers');
      if (customers.isNotEmpty) {
        final firstCustomer = customers.first;
        print('🔍 [PDF DEBUG] First customer in PDF:');
        print('   - Name: ${firstCustomer.customerName}');
        print('   - Total Debt: ${firstCustomer.totalDebt}');
        print('   - Outstanding Debt: ${firstCustomer.outstandingDebt}');
        print('   - Paid Amount: ${firstCustomer.paidAmount}');
      }
    }

    // Calculate totals
    final totalCustomers = customers.length;
    final totalDebtAmount = customers.fold<double>(
        0.0, (sum, customer) => sum + customer.totalDebt);
    final totalOutstandingAmount = customers.fold<double>(
        0.0, (sum, customer) => sum + customer.outstandingDebt);
    final totalPaidAmount = customers.fold<double>(
        0.0, (sum, customer) => sum + customer.paidAmount);

    if (kDebugMode) {
      print('🔍 [PDF DEBUG] Calculated totals:');
      print('   - Total Customers: $totalCustomers');
      print('   - Total Debt Amount: $totalDebtAmount');
      print('   - Total Outstanding Amount: $totalOutstandingAmount');
      print('   - Total Paid Amount: $totalPaidAmount');
    }

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'CUSTOMER DETAILS',
          style: pw.TextStyle(
            fontSize: 16,
            fontWeight: pw.FontWeight.bold,
          ),
        ),
        pw.SizedBox(height: 10),
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.grey),
          columnWidths: {
            0: const pw.FlexColumnWidth(2), // Customer Name
            1: const pw.FlexColumnWidth(1), // Type
            2: const pw.FlexColumnWidth(2), // Phone
            3: const pw.FlexColumnWidth(1.5), // Risk Level
            4: const pw.FlexColumnWidth(1.5), // Total Debt
            5: const pw.FlexColumnWidth(1.5), // Outstanding
            6: const pw.FlexColumnWidth(1.5), // Paid Amount
          },
          children: [
            // Header row
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.grey200),
              children: [
                _buildTableCell('Customer Name', isHeader: true),
                _buildTableCell('Type', isHeader: true),
                _buildTableCell('Phone', isHeader: true),
                _buildTableCell('Risk Level', isHeader: true),
                _buildTableCell('Debt', isHeader: true),
                _buildTableCell('Outstanding', isHeader: true),
                _buildTableCell('Paid Amount', isHeader: true),
              ],
            ),
            // Data rows
            ...customers.map((customer) => pw.TableRow(
                  children: [
                    _buildTableCell(customer.customerName),
                    _buildTableCell(customer.customerType),
                    _buildTableCell(customer.phone),
                    _buildTableCell(customer.riskLevel),
                    _buildTableCell(
                        '\$${customer.totalDebt.toStringAsFixed(2)}'),
                    _buildTableCell(
                        '\$${customer.outstandingDebt.toStringAsFixed(2)}'),
                    _buildTableCell(
                        '\$${customer.paidAmount.toStringAsFixed(2)}'),
                  ],
                )),
            // Totals row
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.grey100),
              children: [
                _buildTableCell('Customers: $totalCustomers',
                    isHeader: true, isTotal: true),
                _buildTableCell('', isHeader: true),
                _buildTableCell('', isHeader: true),
                _buildTableCell('', isHeader: true),
                _buildTableCell(
                    'Total: \$${totalDebtAmount.toStringAsFixed(2)}',
                    isHeader: true,
                    isTotal: true),
                _buildTableCell(
                    'Total: \$${totalOutstandingAmount.toStringAsFixed(2)}',
                    isHeader: true,
                    isTotal: true),
                _buildTableCell(
                    'Total: \$${totalPaidAmount.toStringAsFixed(2)}',
                    isHeader: true,
                    isTotal: true),
              ],
            ),
          ],
        ),
      ],
    );
  }

  /// Build table cell with consistent styling
  static pw.Widget _buildTableCell(String text,
      {bool isHeader = false, bool isTotal = false}) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontSize: isHeader ? 12 : 10,
          fontWeight:
              (isHeader || isTotal) ? pw.FontWeight.bold : pw.FontWeight.normal,
          color: isTotal ? PdfColors.blue800 : PdfColors.black,
        ),
      ),
    );
  }

  /// Build PDF footer section
  static pw.Widget _buildFooter() {
    return pw.Column(
      children: [
        pw.Divider(),
        pw.Text(
          'Generated by DeynCare - Debt Management System',
          style: pw.TextStyle(
            fontSize: 10,
            color: PdfColors.grey,
            fontStyle: pw.FontStyle.italic,
          ),
        ),
      ],
    );
  }

  /// Generate simple text report content (kept for fallback)
  static String _generateTextReport(
    List<CustomerReportData> customers,
    Map<String, dynamic> summary,
    String reportPeriod,
    String shopName,
  ) {
    final buffer = StringBuffer();

    // Header
    buffer.writeln('======================================');
    buffer.writeln('         CUSTOMER REPORT');
    buffer.writeln('======================================');
    buffer.writeln('Shop: $shopName');
    buffer.writeln('Period: $reportPeriod');
    buffer.writeln('Generated: ${DateTime.now().toString()}');
    buffer.writeln('======================================');
    buffer.writeln();

    // Summary
    buffer.writeln('SUMMARY:');
    buffer.writeln('- Total Customers: ${summary['totalCustomers'] ?? 0}');
    buffer.writeln(
        '- Total Debt Amount: \$${(summary['totalDebtAmount'] ?? 0).toStringAsFixed(2)}');
    buffer.writeln(
        '- Outstanding Debt: \$${(summary['totalOutstandingDebt'] ?? 0).toStringAsFixed(2)}');
    buffer.writeln();

    // Customer Details
    buffer.writeln('CUSTOMER DETAILS:');
    buffer.writeln('─────────────────────────────────────');

    for (final customer in customers) {
      buffer.writeln('Customer ID: ${customer.customerId}');
      buffer.writeln('Name: ${customer.customerName}');
      buffer.writeln('Type: ${customer.customerType}');
      buffer.writeln('Phone: ${customer.phone}');
      buffer.writeln('Email: ${customer.email}');
      buffer.writeln('Risk Level: ${customer.riskLevel}');
      buffer.writeln('Total Debt: \$${customer.totalDebt.toStringAsFixed(2)}');
      buffer.writeln(
          'Outstanding: \$${customer.outstandingDebt.toStringAsFixed(2)}');
      buffer
          .writeln('Paid Amount: \$${customer.paidAmount.toStringAsFixed(2)}');
      buffer.writeln('Payment Status: ${customer.paymentStatus}');
      buffer.writeln('Registration: ${customer.registrationDate}');
      buffer.writeln(
          'Days Since Registration: ${customer.daysSinceRegistration}');
      buffer.writeln('─────────────────────────────────────');
    }

    buffer.writeln();
    buffer.writeln('End of Report - Generated by DeynCare');

    return buffer.toString();
  }

  /// Share PDF file using share_plus package
  static Future<void> sharePDF(String filePath, String fileName) async {
    try {
      if (kDebugMode) {
        print('📤 Sharing PDF file: $filePath');
        print('📄 File name: $fileName');
      }

      final file = File(filePath);
      if (!await file.exists()) {
        throw Exception('PDF file not found: $filePath');
      }

      // Verify file is readable
      final fileSize = await file.length();
      if (fileSize == 0) {
        throw Exception('PDF file is empty or corrupted');
      }

      // Share the PDF file using share_plus
      await Share.shareXFiles(
        [XFile(filePath)],
        text: 'Report generated by DeynCare',
        subject: fileName,
      );

      if (kDebugMode) {
        print('✅ PDF shared successfully (${fileSize} bytes)');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error sharing PDF file: $e');
      }

      // Provide user-friendly error messages
      if (e.toString().contains('not found')) {
        throw Exception('PDF file not found. Please try generating the report again.');
      } else if (e.toString().contains('empty') || e.toString().contains('corrupted')) {
        throw Exception('PDF file is corrupted. Please try generating the report again.');
      } else {
        throw Exception('Unable to share PDF. Please check your device settings and try again.');
      }
    }
  }

  /// Open PDF file using system default PDF viewer with FileProvider
  static Future<void> openPDF(String filePath) async {
    try {
      if (kDebugMode) {
        print('📂 Opening PDF file: $filePath');
      }

      final file = File(filePath);
      if (!await file.exists()) {
        throw Exception('PDF file not found: $filePath');
      }

      // Verify file is not empty or corrupted
      final fileSize = await file.length();
      if (fileSize == 0) {
        throw Exception('PDF file is empty or corrupted');
      }

      // Add timeout to prevent hanging
      await Future.any([
        _openPDFWithTimeout(filePath),
        Future.delayed(const Duration(seconds: 10), () => throw TimeoutException('PDF opening timed out', const Duration(seconds: 10)))
      ]);

    } catch (e) {
      if (kDebugMode) {
        print('❌ Error opening PDF file: $e');
        print('🔄 Falling back to sharing...');
      }

      // Try sharing as fallback - this should never crash the app
      try {
        await sharePDF(filePath, 'Report');
      } catch (shareError) {
        if (kDebugMode) {
          print('❌ Error sharing PDF as fallback: $shareError');
        }
        // Final fallback - show user-friendly error instead of crashing
        throw Exception('Unable to open or share PDF. Please check if you have a PDF viewer app installed.');
      }
    }
  }

  /// Open PDF with timeout handling
  static Future<void> _openPDFWithTimeout(String filePath) async {
    if (Platform.isAndroid) {
      // Use Android FileProvider for secure file access
      await _openPDFWithFileProvider(filePath);
    } else {
      // For iOS, use direct file URI
      await _openPDFOnIOS(filePath);
    }
  }

  /// Open PDF on iOS with proper error handling
  static Future<void> _openPDFOnIOS(String filePath) async {
    try {
      final uri = Uri.file(filePath);
      final canLaunch = await canLaunchUrl(uri);

      if (canLaunch) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
        if (kDebugMode) {
          print('✅ PDF opened successfully on iOS');
        }
      } else {
        // Try alternative approach with open_file package
        throw Exception('Cannot launch PDF with url_launcher on iOS');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ iOS PDF opening failed: $e');
      }
      rethrow;
    }
  }

  /// Open PDF using Android FileProvider for secure access
  static Future<void> _openPDFWithFileProvider(String filePath) async {
    try {
      // Use platform channel to open PDF with FileProvider
      const platform = MethodChannel('com.deyncare.app/file_provider');

      final result = await platform.invokeMethod('openPDF', {
        'filePath': filePath,
      });

      if (kDebugMode) {
        print('✅ PDF opened with FileProvider: $result');
      }
    } on PlatformException catch (e) {
      if (kDebugMode) {
        print('❌ FileProvider error: ${e.code} - ${e.message}');
        print('❌ Error details: ${e.details}');
      }

      // Handle specific error cases
      if (e.code == 'OPEN_ERROR' && e.message?.contains('No PDF viewer app found') == true) {
        throw Exception('No PDF viewer app found. Please install a PDF reader app from the Play Store.');
      } else if (e.code == 'INVALID_ARGUMENT') {
        throw Exception('Invalid file path provided.');
      } else {
        throw Exception('Failed to open PDF: ${e.message ?? 'Unknown error'}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Unexpected error in FileProvider: $e');
      }
      throw Exception('Unexpected error opening PDF: $e');
    }
  }

  /// Generate Debt Report PDF
  static Future<String> generateDebtReport({
    required List<DebtReportData> debts,
    required Map<String, dynamic> summary,
    required String reportPeriod,
    required String shopName,
    String? shopLogo,
  }) async {
    try {
      if (kDebugMode) {
        print('📄 [PDF] Starting debt report generation...');
        print('📄 [PDF] Debts count: ${debts.length}');
        print('📄 [PDF] Shop: $shopName');
      }

      final pdf = pw.Document();

      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(24),
          build: (pw.Context context) {
            return [
              _buildDebtHeader(shopName, reportPeriod),
              pw.SizedBox(height: 20),
              _buildDebtSummary(summary),
              pw.SizedBox(height: 20),
              ..._buildPaginatedDebtTable(debts),
              pw.SizedBox(height: 20),
              _buildFooter(),
            ];
          },
        ),
      );

      // Save PDF to file
      final now = DateTime.now();
      final fileName =
          'Debt_Report_${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}_${now.hour.toString().padLeft(2, '0')}${now.minute.toString().padLeft(2, '0')}${now.second.toString().padLeft(2, '0')}.pdf';

      final directory = await getExternalStorageDirectory();
      final reportsDir = Directory('${directory!.path}/Download/DeynCare');

      if (!await reportsDir.exists()) {
        await reportsDir.create(recursive: true);
      }

      final file = File('${reportsDir.path}/$fileName');
      final pdfBytes = await pdf.save();
      await file.writeAsBytes(pdfBytes);

      if (kDebugMode) {
        print('💾 Debt PDF saved: ${file.path}');
      }

      return file.path;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [PDF] Error generating debt report: $e');
      }
      rethrow;
    }
  }

  /// Generate Risk Report PDF
  static Future<String> generateRiskReport({
    required List<RiskReportData> risks,
    required Map<String, dynamic> summary,
    required String reportPeriod,
    required String shopName,
    String? shopLogo,
  }) async {
    try {
      if (kDebugMode) {
        print('📄 [PDF] Starting risk report generation...');
        print('📄 [PDF] Risks count: ${risks.length}');
        print('📄 [PDF] Shop: $shopName');
      }

      final pdf = pw.Document();

      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(24),
          build: (pw.Context context) {
            return [
              _buildRiskHeader(shopName, reportPeriod),
              pw.SizedBox(height: 20),
              _buildRiskSummary(summary),
              pw.SizedBox(height: 20),
              ..._buildPaginatedRiskTable(risks),
              pw.SizedBox(height: 20),
              _buildFooter(),
            ];
          },
        ),
      );

      // Save PDF to file
      final now = DateTime.now();
      final fileName =
          'Risk_Report_${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}_${now.hour.toString().padLeft(2, '0')}${now.minute.toString().padLeft(2, '0')}${now.second.toString().padLeft(2, '0')}.pdf';

      final directory = await getExternalStorageDirectory();
      final reportsDir = Directory('${directory!.path}/Download/DeynCare');

      if (!await reportsDir.exists()) {
        await reportsDir.create(recursive: true);
      }

      final file = File('${reportsDir.path}/$fileName');
      final pdfBytes = await pdf.save();
      await file.writeAsBytes(pdfBytes);

      if (kDebugMode) {
        print('💾 Risk PDF saved: ${file.path}');
      }

      return file.path;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [PDF] Error generating risk report: $e');
      }
      rethrow;
    }
  }

  /// Build debt report header section
  static pw.Widget _buildDebtHeader(String shopName, String reportPeriod) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'DEBT REPORT',
          style: pw.TextStyle(
            fontSize: 24,
            fontWeight: pw.FontWeight.bold,
            color: PdfColors.blue800,
          ),
        ),
        pw.SizedBox(height: 8),
        pw.Text(
          shopName,
          style: pw.TextStyle(
            fontSize: 18,
            fontWeight: pw.FontWeight.bold,
          ),
        ),
        pw.SizedBox(height: 4),
        pw.Text(
          'Report Period: $reportPeriod',
          style: const pw.TextStyle(fontSize: 12),
        ),
        pw.Text(
          'Generated: ${DateTime.now().toString().split('.')[0]}',
          style: const pw.TextStyle(fontSize: 12),
        ),
        pw.SizedBox(height: 16),
        pw.Divider(thickness: 2, color: PdfColors.blue800),
      ],
    );
  }

  /// Build risk report header section
  static pw.Widget _buildRiskHeader(String shopName, String reportPeriod) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'RISK ASSESSMENT REPORT',
          style: pw.TextStyle(
            fontSize: 24,
            fontWeight: pw.FontWeight.bold,
            color: PdfColors.red800,
          ),
        ),
        pw.SizedBox(height: 8),
        pw.Text(
          shopName,
          style: pw.TextStyle(
            fontSize: 18,
            fontWeight: pw.FontWeight.bold,
          ),
        ),
        pw.SizedBox(height: 4),
        pw.Text(
          'Report Period: $reportPeriod',
          style: const pw.TextStyle(fontSize: 12),
        ),
        pw.Text(
          'Generated: ${DateTime.now().toString().split('.')[0]}',
          style: const pw.TextStyle(fontSize: 12),
        ),
        pw.SizedBox(height: 16),
        pw.Divider(thickness: 2, color: PdfColors.red800),
      ],
    );
  }

  /// Build debt summary section
  static pw.Widget _buildDebtSummary(Map<String, dynamic> summary) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey400),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'Debt Summary',
            style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
          ),
          pw.SizedBox(height: 10),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Expanded(
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text('Total Debts: ${summary['totalDebts'] ?? 0}'),
                    pw.Text(
                        'Total Amount: \$${((summary['totalDebtAmount'] as num?)?.toDouble() ?? 0.0).toStringAsFixed(2)}'),
                  ],
                ),
              ),
              pw.Expanded(
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                        'Outstanding: \$${((summary['totalOutstandingDebt'] as num?)?.toDouble() ?? 0.0).toStringAsFixed(2)}'),
                    pw.Text(
                        'Paid: \$${((summary['totalPaidAmount'] as num?)?.toDouble() ?? 0.0).toStringAsFixed(2)}'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build paginated debt table that can span multiple pages
  static List<pw.Widget> _buildPaginatedDebtTable(List<DebtReportData> debts) {
    if (debts.isEmpty) {
      return [
        pw.Container(
          padding: const pw.EdgeInsets.all(16),
          child: pw.Text('No debt data available for the selected period.'),
        ),
      ];
    }

    const int rowsPerPage =
        12; // Conservative limit to prevent TooManyPagesException
    final List<pw.Widget> widgets = [];

    // Add table header
    widgets.add(
      pw.Text(
        'Debt Details',
        style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold),
      ),
    );
    widgets.add(pw.SizedBox(height: 10));

    // Split debts into chunks for pagination
    for (int i = 0; i < debts.length; i += rowsPerPage) {
      final chunk = debts.skip(i).take(rowsPerPage).toList();

      widgets.add(
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.grey400),
          columnWidths: {
            0: const pw.FlexColumnWidth(2.0), // Customer
            1: const pw.FlexColumnWidth(1.3), // Debt Amount
            2: const pw.FlexColumnWidth(1.3), // Outstanding
            3: const pw.FlexColumnWidth(1.3), // Paid Amount
            4: const pw.FlexColumnWidth(1.2), // Debt Date
            5: const pw.FlexColumnWidth(1.2), // Due Date
            6: const pw.FlexColumnWidth(1.3), // Overdue
          },
          children: [
            // Header row (only for first chunk)
            if (i == 0)
              pw.TableRow(
                decoration: const pw.BoxDecoration(color: PdfColors.grey200),
                children: [
                  _buildTableCell('Customer', isHeader: true),
                  _buildTableCell('Debt Amount', isHeader: true),
                  _buildTableCell('Outstanding', isHeader: true),
                  _buildTableCell('Paid Amount', isHeader: true),
                  _buildTableCell('Debt Date', isHeader: true),
                  _buildTableCell('Due Date', isHeader: true),
                  _buildTableCell('Overdue', isHeader: true),
                ],
              ),
            // Data rows for this chunk
            ...chunk.map((debt) => pw.TableRow(
                  children: [
                    _buildTableCell(debt.customerName),
                    _buildTableCell('\$${debt.debtAmount.toStringAsFixed(2)}'),
                    _buildTableCell(
                        '\$${debt.outstandingDebt.toStringAsFixed(2)}'),
                    _buildTableCell('\$${debt.paidAmount.toStringAsFixed(2)}'),
                    _buildTableCell(
                        _formatDate(debt.debtDate.toIso8601String())),
                    _buildTableCell(
                        _formatDate(debt.dueDate?.toIso8601String())),
                    _buildTableCell('\$${debt.overdueDebt.toStringAsFixed(2)}'),
                  ],
                )),
          ],
        ),
      );

      // Add spacing between table chunks
      if (i + rowsPerPage < debts.length) {
        widgets.add(pw.SizedBox(height: 20));
      }
    }

    return widgets;
  }

  /// Build debt table with column totals (DEPRECATED - replaced by paginated version)
  static pw.Widget _buildDebtTableWithTotals(
      List<DebtReportData> debts, Map<String, dynamic> summary) {
    if (kDebugMode) {
      print('🔍 [PDF DEBUG] Building debt table with ${debts.length} debts');
      if (debts.isNotEmpty) {
        final firstDebt = debts.first;
        print('🔍 [PDF DEBUG] First debt in PDF:');
        print('   - Customer: ${firstDebt.customerName}');
        print('   - Debt Amount: ${firstDebt.debtAmount}');
        print('   - Outstanding Debt: ${firstDebt.outstandingDebt}');
        print('   - Paid Amount: ${firstDebt.paidAmount}');
      }
    }

    // Calculate totals
    final totalDebts = debts.length;
    final totalDebtAmount =
        debts.fold<double>(0.0, (sum, debt) => sum + debt.debtAmount);
    final totalOutstandingAmount =
        debts.fold<double>(0.0, (sum, debt) => sum + debt.outstandingDebt);
    final totalPaidAmount =
        debts.fold<double>(0.0, (sum, debt) => sum + debt.paidAmount);
    final totalOverdueAmount =
        debts.fold<double>(0.0, (sum, debt) => sum + debt.overdueDebt);

    if (kDebugMode) {
      print('🔍 [PDF DEBUG] Calculated debt totals:');
      print('   - Total Debts: $totalDebts');
      print('   - Total Debt Amount: $totalDebtAmount');
      print('   - Total Outstanding Amount: $totalOutstandingAmount');
      print('   - Total Paid Amount: $totalPaidAmount');
      print('   - Total Overdue Amount: $totalOverdueAmount');
    }

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'Debt Details',
          style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 10),
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.grey400),
          columnWidths: {
            0: const pw.FlexColumnWidth(2.5), // Customer
            1: const pw.FlexColumnWidth(1.5), // Debt Amount
            2: const pw.FlexColumnWidth(1.5), // Outstanding
            3: const pw.FlexColumnWidth(1.5), // Paid Amount
            4: const pw.FlexColumnWidth(1.5), // Debt Date
            5: const pw.FlexColumnWidth(1.5), // Due Date
            6: const pw.FlexColumnWidth(1.5), // Overdue
          },
          children: [
            // Header row
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.grey200),
              children: [
                _buildTableCell('Customer', isHeader: true),
                _buildTableCell('Debt Amount', isHeader: true),
                _buildTableCell('Outstanding', isHeader: true),
                _buildTableCell('Paid Amount', isHeader: true),
                _buildTableCell('Debt Date', isHeader: true),
                _buildTableCell('Due Date', isHeader: true),
                _buildTableCell('Overdue', isHeader: true),
              ],
            ),
            // Data rows
            ...debts.map((debt) => pw.TableRow(
                  children: [
                    _buildTableCell(debt.customerName),
                    _buildTableCell('\$${debt.debtAmount.toStringAsFixed(2)}'),
                    _buildTableCell(
                        '\$${debt.outstandingDebt.toStringAsFixed(2)}'),
                    _buildTableCell('\$${debt.paidAmount.toStringAsFixed(2)}'),
                    _buildTableCell(
                        _formatDate(debt.debtDate.toIso8601String())),
                    _buildTableCell(
                        _formatDate(debt.dueDate?.toIso8601String())),
                    _buildTableCell('\$${debt.overdueDebt.toStringAsFixed(2)}'),
                  ],
                )),
            // Totals row
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.grey100),
              children: [
                _buildTableCell('Debts: $totalDebts',
                    isHeader: true, isTotal: true),
                _buildTableCell(
                    'Total: \$${totalDebtAmount.toStringAsFixed(2)}',
                    isHeader: true,
                    isTotal: true),
                _buildTableCell(
                    'Total: \$${totalOutstandingAmount.toStringAsFixed(2)}',
                    isHeader: true,
                    isTotal: true),
                _buildTableCell(
                    'Total: \$${totalPaidAmount.toStringAsFixed(2)}',
                    isHeader: true,
                    isTotal: true),
                _buildTableCell('', isHeader: true),
                _buildTableCell('', isHeader: true),
                _buildTableCell(
                    'Total: \$${totalOverdueAmount.toStringAsFixed(2)}',
                    isHeader: true,
                    isTotal: true),
              ],
            ),
          ],
        ),
      ],
    );
  }

  /// Build risk summary section
  static pw.Widget _buildRiskSummary(Map<String, dynamic> summary) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey400),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'Risk Assessment Summary',
            style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
          ),
          pw.SizedBox(height: 10),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Expanded(
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                        'Total Customers: ${summary['totalCustomers'] ?? 0}'),
                    pw.Text('High Risk: ${summary['highRiskCount'] ?? 0}'),
                  ],
                ),
              ),
              pw.Expanded(
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text('Medium Risk: ${summary['mediumRiskCount'] ?? 0}'),
                    pw.Text('Low Risk: ${summary['lowRiskCount'] ?? 0}'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build paginated risk table that can span multiple pages
  static List<pw.Widget> _buildPaginatedRiskTable(List<RiskReportData> risks) {
    if (risks.isEmpty) {
      return [
        pw.Container(
          padding: const pw.EdgeInsets.all(16),
          child: pw.Text('No risk data available for the selected period.'),
        ),
      ];
    }

    const int rowsPerPage =
        12; // Conservative limit to prevent TooManyPagesException
    final List<pw.Widget> widgets = [];

    // Add table header
    widgets.add(
      pw.Text(
        'Risk Assessment Details',
        style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold),
      ),
    );
    widgets.add(pw.SizedBox(height: 10));

    // Split risks into chunks for pagination
    for (int i = 0; i < risks.length; i += rowsPerPage) {
      final chunk = risks.skip(i).take(rowsPerPage).toList();

      widgets.add(
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.grey400),
          columnWidths: {
            0: const pw.FlexColumnWidth(2.0), // Customer
            1: const pw.FlexColumnWidth(1.2), // Customer Type
            2: const pw.FlexColumnWidth(1.3), // Risk Level
            3: const pw.FlexColumnWidth(1.3), // Total Debt
            4: const pw.FlexColumnWidth(1.3), // Paid Amount
            5: const pw.FlexColumnWidth(1.2), // Payment Delay
          },
          children: [
            // Header row (only for first chunk)
            if (i == 0)
              pw.TableRow(
                decoration: const pw.BoxDecoration(color: PdfColors.grey200),
                children: [
                  _buildTableCell('Customer', isHeader: true),
                  _buildTableCell('Type', isHeader: true),
                  _buildTableCell('Risk Level', isHeader: true),
                  _buildTableCell('Total Debt', isHeader: true),
                  _buildTableCell('Paid Amount', isHeader: true),
                  _buildTableCell('Payment Delay', isHeader: true),
                ],
              ),
            // Data rows for this chunk
            ...chunk.map((risk) => pw.TableRow(
                  children: [
                    _buildTableCell(risk.customerName),
                    _buildTableCell(risk.customerType),
                    _buildTableCell(risk.riskLevel),
                    _buildTableCell('\$${risk.totalDebt.toStringAsFixed(2)}'),
                    _buildTableCell('\$${risk.paidAmount.toStringAsFixed(2)}'),
                    _buildTableCell('${risk.paymentDelay} days'),
                  ],
                )),
          ],
        ),
      );

      // Add spacing between table chunks
      if (i + rowsPerPage < risks.length) {
        widgets.add(pw.SizedBox(height: 20));
      }
    }

    return widgets;
  }

  /// Build risk table with column totals (DEPRECATED - replaced by paginated version)
  static pw.Widget _buildRiskTableWithTotals(
      List<RiskReportData> risks, Map<String, dynamic> summary) {
    if (kDebugMode) {
      print('🔍 [PDF DEBUG] Building risk table with ${risks.length} risks');
      if (risks.isNotEmpty) {
        final firstRisk = risks.first;
        print('🔍 [PDF DEBUG] First risk in PDF:');
        print('   - Customer: ${firstRisk.customerName}');
        print('   - Risk Level: ${firstRisk.riskLevel}');
        print('   - Total Debt: ${firstRisk.totalDebt}');
        print('   - Payment Delay: ${firstRisk.paymentDelay}');
      }
    }

    // Calculate totals
    final totalCustomers = risks.length;
    final totalDebtAmount =
        risks.fold<double>(0.0, (sum, risk) => sum + risk.totalDebt);
    final totalPaidAmount =
        risks.fold<double>(0.0, (sum, risk) => sum + risk.paidAmount);
    final averagePaymentDelay = risks.isNotEmpty
        ? risks.fold<double>(
                0.0, (sum, risk) => sum + risk.paymentDelay.toDouble()) /
            risks.length
        : 0.0;
    final highRiskCount = risks.where((risk) => risk.riskScore >= 70).length;

    if (kDebugMode) {
      print('🔍 [PDF DEBUG] Calculated risk totals:');
      print('   - Total Customers: $totalCustomers');
      print('   - Total Debt Amount: $totalDebtAmount');
      print('   - Total Paid Amount: $totalPaidAmount');
      print('   - Average Payment Delay: $averagePaymentDelay');
      print('   - High Risk Count: $highRiskCount');
    }

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'Risk Assessment Details',
          style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 10),
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.grey400),
          columnWidths: {
            0: const pw.FlexColumnWidth(2.5), // Customer
            1: const pw.FlexColumnWidth(1.5), // Type
            2: const pw.FlexColumnWidth(1.5), // Risk Level
            3: const pw.FlexColumnWidth(1.5), // Total Debt
            4: const pw.FlexColumnWidth(1.5), // Paid Amount
            5: const pw.FlexColumnWidth(1.5), // Payment Delay
          },
          children: [
            // Header row
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.grey200),
              children: [
                _buildTableCell('Customer', isHeader: true),
                _buildTableCell('Type', isHeader: true),
                _buildTableCell('Risk Level', isHeader: true),
                _buildTableCell('Total Debt', isHeader: true),
                _buildTableCell('Paid Amount', isHeader: true),
                _buildTableCell('Payment Delay', isHeader: true),
              ],
            ),
            // Data rows
            ...risks.map((risk) => pw.TableRow(
                  children: [
                    _buildTableCell(risk.customerName),
                    _buildTableCell(risk.customerType),
                    _buildTableCell(risk.riskLevel),
                    _buildTableCell('\$${risk.totalDebt.toStringAsFixed(2)}'),
                    _buildTableCell('\$${risk.paidAmount.toStringAsFixed(2)}'),
                    _buildTableCell('${risk.paymentDelay} days'),
                  ],
                )),
            // Totals row
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.grey100),
              children: [
                _buildTableCell('Customers: $totalCustomers',
                    isHeader: true, isTotal: true),
                _buildTableCell('High Risk: $highRiskCount',
                    isHeader: true, isTotal: true),
                _buildTableCell('', isHeader: true),
                _buildTableCell(
                    'Total: \$${totalDebtAmount.toStringAsFixed(2)}',
                    isHeader: true,
                    isTotal: true),
                _buildTableCell(
                    'Total: \$${totalPaidAmount.toStringAsFixed(2)}',
                    isHeader: true,
                    isTotal: true),
                _buildTableCell(
                    'Avg: ${averagePaymentDelay.toStringAsFixed(0)} days',
                    isHeader: true,
                    isTotal: true),
              ],
            ),
          ],
        ),
      ],
    );
  }

  /// Format date for display in PDF
  static String _formatDate(dynamic date) {
    if (date == null) return 'N/A';

    try {
      DateTime dateTime;
      if (date is String) {
        dateTime = DateTime.parse(date);
      } else if (date is DateTime) {
        dateTime = date;
      } else {
        return 'Invalid Date';
      }

      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    } catch (e) {
      return 'Invalid Date';
    }
  }
}
